// Package models contains core data structures for the decision tree implementation.
package models

import (
	"fmt"
	"strings"
	"time"
)

// FeatureType represents the type of a feature
type FeatureType string

const (
	// NumericFeature represents a numerical feature
	NumericFeature FeatureType = "numeric"
	// CategoricalFeature represents a categorical feature
	CategoricalFeature FeatureType = "categorical"
	// DateFeature represents a date feature
	DateFeature FeatureType = "date"
)

// ModelError represents errors in model operations
type ModelError struct {
	Op     string
	Field  string
	Value  string
	Reason string
	Err    error
}

func (e *ModelError) Error() string {
	if e.Field != "" {
		return fmt.Sprintf("model %s error for field '%s': %s", e.Op, e.Field, e.Reason)
	}
	return fmt.Sprintf("model %s error: %s", e.Op, e.Reason)
}

func (e *ModelError) Unwrap() error {
	return e.Err
}

// Range represents a numeric range with validation
type Range struct {
	Min float64 `json:"min"`
	Max float64 `json:"max"`
}

// NewRange creates a validated numeric range
func NewRange(min, max float64) (*Range, error) {
	if min > max {
		return nil, &ModelError{
			Op:     "create_range",
			Reason: fmt.Sprintf("min value (%.6f) cannot be greater than max value (%.6f)", min, max),
		}
	}
	if min == max {
		return nil, &ModelError{
			Op:     "create_range",
			Reason: fmt.Sprintf("min and max values cannot be equal (%.6f)", min),
		}
	}
	return &Range{Min: min, Max: max}, nil
}

// Contains checks if a value is within the range
func (r *Range) Contains(value float64) bool {
	return value >= r.Min && value <= r.Max
}

// DateRange represents a date range with validation
type DateRange struct {
	Start time.Time `json:"start"`
	End   time.Time `json:"end"`
}

// NewDateRange creates a validated date range
func NewDateRange(start, end time.Time) (*Range, error) {
	if start.After(end) {
		return nil, &ModelError{
			Op:     "create_date_range",
			Reason: "start date cannot be after end date",
		}
	}
	if start.Equal(end) {
		return nil, &ModelError{
			Op:     "create_date_range",
			Reason: "start and end dates cannot be equal",
		}
	}
	return &Range{
		Min: float64(start.Unix()),
		Max: float64(end.Unix()),
	}, nil
}

// Feature represents a feature in the dataset
type Feature struct {
	Name              string        `json:"name"`          // Name of the feature (column name)
	Type              FeatureType   `json:"type"`          // Type of the feature (numeric, categorical, date)
	ColumnNumber      int           `json:"column_number"` // Column number (0-indexed)
	Values            []interface{} `json:"values"`        // Deprecated, use CategoricalValues
	CategoricalValues []string      `json:"categorical_values,omitempty"`
	NumericRange      *Range        `json:"numeric_range,omitempty"`
	DateRange         *DateRange    `json:"date_range,omitempty"`

	// Legacy fields for backward compatibility
	Min float64 `json:"min,omitempty"` // Min value (deprecated)
	Max float64 `json:"max,omitempty"` // Max value (deprecated)
}

// isValidFeatureType checks if a feature type is valid
func isValidFeatureType(featureType FeatureType) bool {
	switch featureType {
	case NumericFeature, CategoricalFeature, DateFeature:
		return true
	default:
		return false
	}
}

// NewFeature creates a new feature with validation
func NewFeature(name string, featureType FeatureType, columnNumber int) (*Feature, error) {
	// Validate inputs
	trimmedName := strings.TrimSpace(name)
	if trimmedName == "" {
		return nil, &ModelError{
			Op:     "create_feature",
			Field:  "name",
			Reason: "feature name cannot be empty or whitespace-only",
		}
	}

	if !isValidFeatureType(featureType) {
		return nil, &ModelError{
			Op:     "create_feature",
			Field:  "type",
			Value:  string(featureType),
			Reason: "invalid feature type, must be 'numeric', 'categorical', or 'date'",
		}
	}

	if columnNumber < 0 {
		return nil, &ModelError{
			Op:     "create_feature",
			Field:  "column_number",
			Value:  fmt.Sprintf("%d", columnNumber),
			Reason: "column number must be non-negative",
		}
	}

	feature := &Feature{
		Name:         trimmedName,
		Type:         featureType,
		ColumnNumber: columnNumber,
		Values:       make([]interface{}, 0), // For backward compatibility
	}

	// Initialize type-specific storage
	switch featureType {
	case CategoricalFeature:
		feature.CategoricalValues = make([]string, 0)
	}

	return feature, nil
}

// Validate performs comprehensive validation of the feature
func (f *Feature) Validate() error {
	if strings.TrimSpace(f.Name) == "" {
		return &ModelError{
			Op:     "validate_feature",
			Field:  "name",
			Reason: "name cannot be empty or whitespace-only",
		}
	}

	if f.ColumnNumber < 0 {
		return &ModelError{
			Op:     "validate_feature",
			Field:  "column_number",
			Value:  fmt.Sprintf("%d", f.ColumnNumber),
			Reason: "must be non-negative",
		}
	}

	switch f.Type {
	case CategoricalFeature:
		return f.validateCategorical()
	case NumericFeature:
		return f.validateNumeric()
	case DateFeature:
		return f.validateDate()
	default:
		return &ModelError{
			Op:     "validate_feature",
			Field:  "type",
			Value:  string(f.Type),
			Reason: "unknown feature type",
		}
	}
}

// validateCategorical validates categorical feature constraints
func (f *Feature) validateCategorical() error {
	// Check modern categorical values
	if len(f.CategoricalValues) > 0 {
		seen := make(map[string]bool)
		for i, value := range f.CategoricalValues {
			if strings.TrimSpace(value) == "" {
				return &ModelError{
					Op:     "validate_categorical",
					Field:  "categorical_values",
					Reason: fmt.Sprintf("value at index %d cannot be empty or whitespace-only", i),
				}
			}

			if seen[value] {
				return &ModelError{
					Op:     "validate_categorical",
					Field:  "categorical_values",
					Value:  value,
					Reason: fmt.Sprintf("duplicate value at index %d", i),
				}
			}
			seen[value] = true
		}
	}

	// Validate legacy Values field if present
	if len(f.Values) > 0 {
		seen := make(map[interface{}]bool)
		for i, value := range f.Values {
			if value == nil {
				return &ModelError{
					Op:     "validate_categorical",
					Field:  "values",
					Reason: fmt.Sprintf("value at index %d cannot be nil", i),
				}
			}

			// Check for empty strings
			if str, ok := value.(string); ok && strings.TrimSpace(str) == "" {
				return &ModelError{
					Op:     "validate_categorical",
					Field:  "values",
					Reason: fmt.Sprintf("string value at index %d cannot be empty", i),
				}
			}

			if seen[value] {
				return &ModelError{
					Op:     "validate_categorical",
					Field:  "values",
					Value:  fmt.Sprintf("%v", value),
					Reason: fmt.Sprintf("duplicate value at index %d", i),
				}
			}
			seen[value] = true
		}
	}

	return nil
}

// validateNumeric validates numeric feature constraints
func (f *Feature) validateNumeric() error {
	// Check modern range
	if f.NumericRange != nil {
		if f.NumericRange.Min > f.NumericRange.Max {
			return &ModelError{
				Op:     "validate_numeric",
				Field:  "numeric_range",
				Reason: fmt.Sprintf("min value (%.6f) cannot be greater than max value (%.6f)", f.NumericRange.Min, f.NumericRange.Max),
			}
		}
		if f.NumericRange.Min == f.NumericRange.Max {
			return &ModelError{
				Op:     "validate_numeric",
				Field:  "numeric_range",
				Reason: fmt.Sprintf("min and max values cannot be equal (%.6f)", f.NumericRange.Min),
			}
		}
	}

	// Check legacy Min/Max fields
	if f.Min != 0 || f.Max != 0 {
		if f.Min > f.Max {
			return &ModelError{
				Op:     "validate_numeric",
				Field:  "min_max_range",
				Reason: fmt.Sprintf("min value (%.6f) cannot be greater than max value (%.6f)", f.Min, f.Max),
			}
		}
		if f.Min == f.Max {
			return &ModelError{
				Op:     "validate_numeric",
				Field:  "min_max_range",
				Reason: fmt.Sprintf("min and max values cannot be equal (%.6f)", f.Min),
			}
		}
	}

	return nil
}

// validateDate validates date feature constraints
func (f *Feature) validateDate() error {
	if f.DateRange != nil {
		if f.DateRange.Start.After(f.DateRange.End) {
			return &ModelError{
				Op:     "validate_date",
				Field:  "date_range",
				Reason: "start date cannot be after end date",
			}
		}
		if f.DateRange.Start.Equal(f.DateRange.End) {
			return &ModelError{
				Op:     "validate_date",
				Field:  "date_range",
				Reason: "start and end dates cannot be equal",
			}
		}
	}

	return nil
}

// SetRange sets the min and max values for numeric features with validation
func (f *Feature) SetRange(min, max float64) error {
	if f.Type != NumericFeature {
		return &ModelError{
			Op:     "set_range",
			Field:  "feature_type",
			Value:  string(f.Type),
			Reason: "can only set range for numeric features",
		}
	}

	if min > max {
		return &ModelError{
			Op:     "set_range",
			Reason: fmt.Sprintf("min value (%.6f) cannot be greater than max value (%.6f)", min, max),
		}
	}

	if min == max {
		return &ModelError{
			Op:     "set_range",
			Reason: fmt.Sprintf("min and max values cannot be equal (%.6f)", min),
		}
	}

	// Set both modern and legacy fields for compatibility
	range_, err := NewRange(min, max)
	if err != nil {
		return err
	}

	f.NumericRange = range_
	f.Min = min
	f.Max = max

	return nil
}

// AddValue adds a possible value for categorical features with validation
// Returns true if the value was added, false if it already existed or couldn't be added
func (f *Feature) AddValue(value interface{}) (bool, error) {
	// Only add values for categorical features
	if f.Type != CategoricalFeature {
		return false, &ModelError{
			Op:     "add_value",
			Field:  "feature_type",
			Value:  string(f.Type),
			Reason: "can only add values to categorical features",
		}
	}

	if value == nil {
		return false, &ModelError{
			Op:     "add_value",
			Field:  "value",
			Reason: "value cannot be nil",
		}
	}

	// Convert to string for modern storage
	strValue := fmt.Sprintf("%v", value)
	if strings.TrimSpace(strValue) == "" {
		return false, &ModelError{
			Op:     "add_value",
			Field:  "value",
			Value:  strValue,
			Reason: "value cannot be empty or whitespace-only",
		}
	}

	// Check if value already exists in modern storage
	for _, existing := range f.CategoricalValues {
		if existing == strValue {
			return false, nil // Already exists, not an error
		}
	}

	// Check if value already exists in legacy storage
	for _, existing := range f.Values {
		if existing == value {
			return false, nil // Already exists, not an error
		}
	}

	// Add to both modern and legacy storage for compatibility
	f.CategoricalValues = append(f.CategoricalValues, strValue)
	f.Values = append(f.Values, value)

	return true, nil
}

// AddCategoricalValue adds a string value specifically for categorical features
func (f *Feature) AddCategoricalValue(value string) (bool, error) {
	if f.Type != CategoricalFeature {
		return false, &ModelError{
			Op:     "add_categorical_value",
			Field:  "feature_type",
			Value:  string(f.Type),
			Reason: "can only add categorical values to categorical features",
		}
	}

	trimmedValue := strings.TrimSpace(value)
	if trimmedValue == "" {
		return false, &ModelError{
			Op:     "add_categorical_value",
			Field:  "value",
			Value:  value,
			Reason: "value cannot be empty or whitespace-only",
		}
	}

	// Check if value already exists
	for _, existing := range f.CategoricalValues {
		if existing == trimmedValue {
			return false, nil // Already exists, not an error
		}
	}

	f.CategoricalValues = append(f.CategoricalValues, trimmedValue)
	return true, nil
}

// GetCategoricalValues returns all categorical values for this feature
func (f *Feature) GetCategoricalValues() []string {
	if f.Type != CategoricalFeature {
		return nil
	}

	// Return copy to prevent external modification
	values := make([]string, len(f.CategoricalValues))
	copy(values, f.CategoricalValues)
	return values
}

// HasCategoricalValue checks if a categorical value exists
func (f *Feature) HasCategoricalValue(value string) bool {
	if f.Type != CategoricalFeature {
		return false
	}

	for _, existing := range f.CategoricalValues {
		if existing == value {
			return true
		}
	}
	return false
}

// GetValueCount returns the number of possible values for categorical features
func (f *Feature) GetValueCount() int {
	if f.Type != CategoricalFeature {
		return 0
	}
	return len(f.CategoricalValues)
}

// IsInRange checks if a numeric value is within the feature's range
func (f *Feature) IsInRange(value float64) (bool, error) {
	if f.Type != NumericFeature {
		return false, &ModelError{
			Op:     "check_range",
			Field:  "feature_type",
			Value:  string(f.Type),
			Reason: "can only check range for numeric features",
		}
	}

	// Check modern range first
	if f.NumericRange != nil {
		return f.NumericRange.Contains(value), nil
	}

	// Fall back to legacy range
	if f.Min != 0 || f.Max != 0 {
		return value >= f.Min && value <= f.Max, nil
	}

	// No range set, accept any value
	return true, nil
}
