// Package main demonstrates the datetime functionality in Mulberri decision trees
package main

import (
	"fmt"
	"log"
	"time"

	datetimeconverter "github.com/berrijam/mulberri/internals/utils/datetime-converter"
	"github.com/berrijam/mulberri/pkg/models"
)

func main() {
	fmt.Println("=== Mulberri DateTime Feature Example ===\n")

	// 1. Demonstrate datetime conversion
	demonstrateDateTimeConversion()

	// 2. Demonstrate feature creation with datetime
	demonstrateDateTimeFeatures()

	// 3. Demonstrate tree node with datetime splits
	demonstrateDateTimeTreeNode()
}

func demonstrateDateTimeConversion() {
	fmt.Println("1. DateTime Conversion Examples")
	fmt.Println("==============================")

	converter := datetimeconverter.NewDateTimeConverter()

	// Example datetime strings
	datetimeStrings := []string{
		"2024-01-15T09:30:45Z",
		"2024-06-20T14:45:30.123Z",
		"2024-12-31T23:59:59Z",
		"2023-02-14T12:00:00+05:30",
		"2023-02-14T12:00:00-08:00",
	}

	fmt.Println("Converting ISO 8601 strings to int64 format:")
	for _, dateStr := range datetimeStrings {
		int64Val, err := converter.ConvertISO8601ToInt64(dateStr)
		if err != nil {
			log.Printf("Error converting %s: %v", dateStr, err)
			continue
		}

		// Convert back to verify
		timeVal, err := converter.ConvertInt64ToTime(int64Val)
		if err != nil {
			log.Printf("Error converting back %d: %v", int64Val, err)
			continue
		}

		fmt.Printf("  %s -> %d -> %s\n", 
			dateStr, int64Val, timeVal.Format("2006-01-02 15:04:05 UTC"))
	}

	// Demonstrate time.Time conversion
	fmt.Println("\nConverting time.Time objects to int64 format:")
	times := []time.Time{
		time.Date(2024, 1, 15, 9, 30, 45, 0, time.UTC),
		time.Date(2024, 6, 20, 14, 45, 30, 0, time.UTC),
		time.Date(2024, 12, 31, 23, 59, 59, 0, time.UTC),
	}

	for _, t := range times {
		int64Val, err := converter.ConvertTimeToInt64(t)
		if err != nil {
			log.Printf("Error converting %v: %v", t, err)
			continue
		}

		fmt.Printf("  %s -> %d\n", 
			t.Format("2006-01-02 15:04:05"), int64Val)
	}

	// Demonstrate batch conversion
	fmt.Println("\nBatch conversion:")
	int64Values, err := converter.BatchConvertToInt64(times)
	if err != nil {
		log.Printf("Error in batch conversion: %v", err)
	} else {
		fmt.Printf("  Batch converted %d times: %v\n", len(times), int64Values)
	}

	fmt.Println()
}

func demonstrateDateTimeFeatures() {
	fmt.Println("2. DateTime Feature Creation")
	fmt.Println("============================")

	// Create a datetime feature
	feature, err := models.NewFeature("transaction_time", models.DateFeature, 0)
	if err != nil {
		log.Fatalf("Error creating datetime feature: %v", err)
	}

	fmt.Printf("Created datetime feature: %s (type: %s, column: %d)\n", 
		feature.Name, feature.Type, feature.ColumnNumber)

	// Validate the feature
	if err := feature.Validate(); err != nil {
		log.Printf("Feature validation failed: %v", err)
	} else {
		fmt.Println("Feature validation passed")
	}

	// Create a date range for the feature
	startTime := time.Date(2024, 1, 1, 0, 0, 0, 0, time.UTC)
	endTime := time.Date(2024, 12, 31, 23, 59, 59, 0, time.UTC)

	dateRange := &models.DateRange{
		Start: startTime,
		End:   endTime,
	}

	feature.DateRange = dateRange

	fmt.Printf("Set date range: %s to %s\n", 
		startTime.Format("2006-01-02"), endTime.Format("2006-01-02"))

	fmt.Println()
}

func demonstrateDateTimeTreeNode() {
	fmt.Println("3. DateTime Tree Node Creation")
	fmt.Println("==============================")

	// Create a datetime feature
	feature, err := models.NewFeature("event_timestamp", models.DateFeature, 1)
	if err != nil {
		log.Fatalf("Error creating datetime feature: %v", err)
	}

	// Create datetime split info
	converter := datetimeconverter.NewDateTimeConverter()
	thresholdTime := time.Date(2024, 6, 15, 12, 0, 0, 0, time.UTC)
	thresholdInt64, err := converter.ConvertTimeToInt64(thresholdTime)
	if err != nil {
		log.Fatalf("Error converting threshold time: %v", err)
	}

	dateTimeInfo := &models.DateTimeSplitInfo{
		ThresholdFormatted: thresholdTime.Format("2006-01-02 15:04:05"),
		SplitDescription:   fmt.Sprintf("Before %s", thresholdTime.Format("January 2, 2006 15:04")),
	}

	// Create a datetime decision node
	node, err := models.NewDateTimeDecisionNode(feature, thresholdInt64, dateTimeInfo)
	if err != nil {
		log.Fatalf("Error creating datetime decision node: %v", err)
	}

	fmt.Printf("Created datetime decision node:\n")
	fmt.Printf("  Feature: %s\n", node.Feature.Name)
	fmt.Printf("  Threshold (int64): %d\n", node.ThresholdInt64)
	fmt.Printf("  Threshold (formatted): %s\n", node.DateTimeInfo.ThresholdFormatted)
	fmt.Printf("  Split description: %s\n", node.DateTimeInfo.SplitDescription)
	fmt.Printf("  Is datetime node: %t\n", node.IsDateTimeNode())

	// Demonstrate threshold retrieval
	if threshold, err := node.GetDateTimeThreshold(); err == nil {
		fmt.Printf("  Retrieved threshold: %d\n", threshold)
	}

	// Validate the node
	if err := node.Validate(); err != nil {
		log.Printf("Node validation failed: %v", err)
	} else {
		fmt.Println("  Node validation passed")
	}

	// Create child nodes to complete the tree structure
	leftChild, err := models.NewLeafNode("before_june", map[interface{}]int{"before_june": 100}, 100)
	if err != nil {
		log.Printf("Error creating left child: %v", err)
	} else {
		node.SetLeftChild(leftChild)
		fmt.Println("  Added left child (before June 15, 2024)")
	}

	rightChild, err := models.NewLeafNode("after_june", map[interface{}]int{"after_june": 150}, 150)
	if err != nil {
		log.Printf("Error creating right child: %v", err)
	} else {
		node.SetRightChild(rightChild)
		fmt.Println("  Added right child (after June 15, 2024)")
	}

	fmt.Printf("  Total child nodes: %d\n", node.GetChildCount())

	fmt.Println()
}

func demonstrateAdvancedDateTimeFeatures() {
	fmt.Println("4. Advanced DateTime Features")
	fmt.Println("=============================")

	converter := datetimeconverter.NewDateTimeConverter()

	// Demonstrate datetime component extraction
	datetimeInt64 := int64(20240615143045) // June 15, 2024 14:30:45

	year, month, day, hour, minute, second, err := converter.GetDateTimeComponents(datetimeInt64)
	if err != nil {
		log.Printf("Error extracting components: %v", err)
	} else {
		fmt.Printf("DateTime %d components:\n", datetimeInt64)
		fmt.Printf("  Year: %d, Month: %d, Day: %d\n", year, month, day)
		fmt.Printf("  Hour: %d, Minute: %d, Second: %d\n", hour, minute, second)
	}

	// Demonstrate datetime validation
	testValues := []int64{
		20240615143045, // Valid
		12345678901234, // Invalid - too small
		99999999999999, // Invalid - too large
		20240229000000, // Valid - leap year
		20230229000000, // Invalid - not a leap year
	}

	fmt.Println("\nDateTime validation:")
	for _, val := range testValues {
		isValid := converter.IsValidDateTimeInt64(val)
		fmt.Printf("  %d: %t\n", val, isValid)
	}

	// Demonstrate formatting
	fmt.Println("\nDateTime formatting:")
	validDateTime := int64(20240615143045)
	
	layouts := []string{
		"2006-01-02 15:04:05",
		"January 2, 2006 at 3:04 PM",
		"2006/01/02 15:04",
		"Mon, 02 Jan 2006 15:04:05 MST",
	}

	for _, layout := range layouts {
		formatted, err := converter.FormatDateTimeInt64(validDateTime, layout)
		if err != nil {
			log.Printf("Error formatting with layout %s: %v", layout, err)
		} else {
			fmt.Printf("  %s: %s\n", layout, formatted)
		}
	}

	fmt.Println()
}
