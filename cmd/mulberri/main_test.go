package main

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"reflect"
	"testing"
	"time"

	"github.com/berrijam/mulberri/cmd/mulberri/cli"
	"github.com/berrijam/mulberri/internals/utils"
	"github.com/berrijam/mulberri/pkg/models"
)

func TestCSVDataset(t *testing.T) {
	features := [][]string{
		{"5.1", "setosa", "red"},
		{"4.9", "versicolor", "blue"},
		{"6.2", "virginica", "red"},
	}
	targets := []string{"iris1", "iris2", "iris3"}

	dataset := utils.NewCSVDataset(features, targets)

	// Test basic properties
	if dataset.GetSize() != 3 {
		t.<PERSON>rrorf("Expected size 3, got %d", dataset.GetSize())
	}

	expectedIndices := []int{0, 1, 2}
	if !reflect.DeepEqual(dataset.GetIndices(), expectedIndices) {
		t.<PERSON><PERSON><PERSON>("Expected indices %v, got %v", expectedIndices, dataset.GetIndices())
	}

	// Test target retrieval
	target, err := dataset.GetTarget(1)
	if err != nil {
		t.Errorf("Unexpected error getting target: %v", err)
	}
	if target != "iris2" {
		t.Errorf("Expected target 'iris2', got '%s'", target)
	}

	// Test numeric feature value retrieval
	numericFeature := &models.Feature{
		Name:         "numeric_feature",
		Type:         models.NumericFeature,
		ColumnNumber: 0,
	}

	value, err := dataset.GetFeatureValue(0, numericFeature)
	if err != nil {
		t.Errorf("Unexpected error getting feature value: %v", err)
	}

	// Should convert to float64 for numeric features
	if floatVal, ok := value.(float64); ok {
		if floatVal != 5.1 {
			t.Errorf("Expected 5.1, got %f", floatVal)
		}
	} else {
		t.Errorf("Expected float64, got %T", value)
	}

	// Test categorical feature value retrieval
	categoricalFeature := &models.Feature{
		Name:         "categorical_feature",
		Type:         models.CategoricalFeature,
		ColumnNumber: 1,
	}

	catValue, err := dataset.GetFeatureValue(0, categoricalFeature)
	if err != nil {
		t.Errorf("Unexpected error getting categorical feature value: %v", err)
	}
	if catValue != "setosa" {
		t.Errorf("Expected 'setosa', got %v", catValue)
	}

	// Test default type feature value retrieval
	defaultFeature := &models.Feature{
		Name:         "default_feature",
		Type:         "unknown_type",
		ColumnNumber: 2,
	}

	defaultValue, err := dataset.GetFeatureValue(0, defaultFeature)
	if err != nil {
		t.Errorf("Unexpected error getting default feature value: %v", err)
	}
	if defaultValue != "red" {
		t.Errorf("Expected 'red', got %v", defaultValue)
	}
}

func TestCSVDatasetSubset(t *testing.T) {
	features := [][]string{
		{"1.0", "a"},
		{"2.0", "b"},
		{"3.0", "c"},
		{"4.0", "d"},
	}
	targets := []string{"x", "y", "z", "w"}

	dataset := utils.NewCSVDataset(features, targets)

	// Create subset with indices [1, 3]
	subset := dataset.Subset([]int{1, 3})

	if subset.GetSize() != 2 {
		t.Errorf("Expected subset size 2, got %d", subset.GetSize())
	}

	expectedIndices := []int{1, 3}
	if !reflect.DeepEqual(subset.GetIndices(), expectedIndices) {
		t.Errorf("Expected subset indices %v, got %v", expectedIndices, subset.GetIndices())
	}

	// Test that subset still references original data
	target, err := subset.GetTarget(1)
	if err != nil {
		t.Errorf("Unexpected error: %v", err)
	}
	if target != "y" {
		t.Errorf("Expected target 'y', got '%s'", target)
	}
}

func TestCSVDatasetErrors(t *testing.T) {
	features := [][]string{{"1.0"}, {"2.0"}}
	targets := []string{"a", "b"}
	dataset := utils.NewCSVDataset(features, targets)

	// Test out of range sample index - negative
	_, err := dataset.GetTarget(-1)
	if err == nil {
		t.Error("Expected error for negative index")
	}

	// Test out of range sample index - too large
	_, err = dataset.GetTarget(10)
	if err == nil {
		t.Error("Expected error for index out of range")
	}

	// Test out of range feature column
	feature := &models.Feature{
		Name:         "test",
		Type:         models.CategoricalFeature,
		ColumnNumber: 5, // Out of range
	}

	_, err = dataset.GetFeatureValue(0, feature)
	if err == nil {
		t.Error("Expected error for feature column out of range")
	}

	// Test negative sample index for GetFeatureValue
	_, err = dataset.GetFeatureValue(-1, feature)
	if err == nil {
		t.Error("Expected error for negative sample index in GetFeatureValue")
	}

	// Test sample index too large for GetFeatureValue
	validFeature := &models.Feature{
		Name:         "valid",
		Type:         models.CategoricalFeature,
		ColumnNumber: 0,
	}
	_, err = dataset.GetFeatureValue(10, validFeature)
	if err == nil {
		t.Error("Expected error for sample index out of range in GetFeatureValue")
	}
}

func TestCSVDatasetPanic(t *testing.T) {
	// Since logger.Fatal() calls os.Exit(1), we can't test this directly in a unit test
	// without the process exiting. Instead, we test the validation logic indirectly
	// by ensuring that valid inputs work and documenting the expected behavior.

	// Test that valid inputs work (no exit)
	features := [][]string{{"1.0"}, {"2.0"}}
	targets := []string{"a", "b"} // Matching length

	dataset := utils.NewCSVDataset(features, targets)
	if dataset.GetSize() != 2 {
		t.Errorf("Expected size 2, got %d", dataset.GetSize())
	}

	// Note: Testing mismatched lengths would cause os.Exit(1) via logger.Fatal()
	// This is the expected behavior for this critical error condition.
}

func TestCSVDatasetNumericConversionFailure(t *testing.T) {
	features := [][]string{
		{"not_a_number", "categorical_value"},
	}
	targets := []string{"target"}

	dataset := utils.NewCSVDataset(features, targets)

	// Test numeric feature with non-numeric value
	numericFeature := &models.Feature{
		Name:         "numeric_feature",
		Type:         models.NumericFeature,
		ColumnNumber: 0,
	}

	value, err := dataset.GetFeatureValue(0, numericFeature)
	if err != nil {
		t.Errorf("Unexpected error: %v", err)
	}

	// Should return as string when numeric conversion fails
	if strVal, ok := value.(string); ok {
		if strVal != "not_a_number" {
			t.Errorf("Expected 'not_a_number', got %v", strVal)
		}
	} else {
		t.Errorf("Expected string when numeric conversion fails, got %T", value)
	}
}

// =============================================================================
// Tests for main.go - Mapping functions
// =============================================================================

func TestStringToModelCriterion(t *testing.T) {
	tests := []struct {
		input    string
		expected models.SplitCriterion
		name     string
	}{
		{"gini", models.GiniCriterion, "gini"},
		{"entropy", models.EntropyCriterion, "entropy"},
		{"mse", models.MSECriterion, "mse"},
		{"unknown", models.EntropyCriterion, "unknown_default"},
		{"", models.EntropyCriterion, "empty_default"},
		{"GINI", models.EntropyCriterion, "case_sensitive"},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			result := StringToModelCriterion(test.input)
			if result != test.expected {
				t.Errorf("StringToModelCriterion(%q) = %v, expected %v", test.input, result, test.expected)
			}
		})
	}
}

func TestMapCriterion(t *testing.T) {
	tests := []struct {
		criterion string
		name      string
	}{
		{"gini", "gini"},
		{"entropy", "entropy"},
		{"mse", "mse"},
		{"unknown", "entropy_default"}, // Default case
		{"", "empty_default"},          // Empty case
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			mapping := mapCriterion(test.criterion)

			// Test that both options are non-nil
			if mapping.SplitterOption == nil {
				t.Error("Expected non-nil splitter option")
			}
			if mapping.BuilderOption == nil {
				t.Error("Expected non-nil builder option")
			}
		})
	}
}

func TestMapCriterionToSplitter(t *testing.T) {
	tests := []struct {
		criterion string
		expected  string
	}{
		{"gini", "gini"},
		{"entropy", "entropy"},
		{"mse", "mse"},
		{"unknown", "entropy"}, // Default case
		{"", "entropy"},        // Empty case
	}

	for _, test := range tests {
		t.Run(test.criterion, func(t *testing.T) {
			option := mapCriterionToSplitter(test.criterion)
			if option == nil {
				t.Error("Expected non-nil splitter option")
			}
		})
	}
}

func TestMapCriterionToBuilder(t *testing.T) {
	tests := []struct {
		criterion string
		expected  string
	}{
		{"gini", "gini"},
		{"entropy", "entropy"},
		{"mse", "mse"},
		{"unknown", "entropy"}, // Default case
		{"", "entropy"},        // Empty case
	}

	for _, test := range tests {
		t.Run(test.criterion, func(t *testing.T) {
			option := mapCriterionToBuilder(test.criterion)
			if option == nil {
				t.Error("Expected non-nil builder option")
			}
		})
	}
}

// =============================================================================
// Tests for main.go - Progress Reporter
// =============================================================================

func TestProgressReporter(t *testing.T) {
	// Test verbose mode
	verboseReporter := NewProgressReporter(true)
	if !verboseReporter.verbose {
		t.Error("Expected verbose reporter to be verbose")
	}

	// Test non-verbose mode
	quietReporter := NewProgressReporter(false)
	if quietReporter.verbose {
		t.Error("Expected quiet reporter to not be verbose")
	}

	// Test stage operations
	verboseReporter.StartStage("test_stage")
	if verboseReporter.stage != "test_stage" {
		t.Errorf("Expected stage 'test_stage', got '%s'", verboseReporter.stage)
	}

	verboseReporter.UpdateProgress("test message")
	verboseReporter.CompleteStage()

	// Test quiet reporter operations (for coverage)
	quietReporter.StartStage("quiet_stage")
	quietReporter.UpdateProgress("quiet message")
	quietReporter.CompleteStage()
}

// =============================================================================
// Tests for main.go - Graceful Shutdown
// =============================================================================

func TestGracefulShutdown(t *testing.T) {
	ctx, cancel := setupGracefulShutdown()
	defer cancel()

	// Test that context is not cancelled initially
	select {
	case <-ctx.Done():
		t.Error("Context should not be cancelled initially")
	default:
		// Expected
	}

	// Test manual cancellation
	cancel()

	// Context should be cancelled now
	select {
	case <-ctx.Done():
		// Expected
		if ctx.Err() != context.Canceled {
			t.Errorf("Expected context.Canceled, got %v", ctx.Err())
		}
	case <-time.After(100 * time.Millisecond):
		t.Error("Context should be cancelled after cancel() call")
	}
}

// =============================================================================
// Tests for main.go - Helper functions
// =============================================================================

func TestShowSuccessMessage(t *testing.T) {
	// Create test tree
	tree := &models.DecisionTree{
		NodeCount: 10,
		LeafCount: 5,
		Depth:     3,
	}

	// Test verbose config
	verboseConfig := &cli.Config{
		Verbose:    true,
		OutputFile: "test_model.json",
	}

	// Test non-verbose config
	quietConfig := &cli.Config{
		Verbose:    false,
		OutputFile: "test_model.json",
	}

	// These mainly test for coverage - no assertions needed
	showSuccessMessage(verboseConfig, tree)
	showSuccessMessage(quietConfig, tree)
}

func TestShowVersion(t *testing.T) {
	// Test version display (mainly for coverage)
	showVersion()
}

func TestHandleCLIError(t *testing.T) {
	// Test with CLIError
	cliErr := &cli.CLIError{
		Op:   "test",
		Flag: "test-flag",
		Err:  fmt.Errorf("test error"),
	}
	handleCLIError(cliErr)

	// Test with regular error
	regularErr := fmt.Errorf("regular error")
	handleCLIError(regularErr)
}

func TestHandleTrainingError(t *testing.T) {
	// Test error handling (mainly for coverage)
	err := fmt.Errorf("training error")
	handleTrainingError(err)
}

// =============================================================================
// Tests for serialization.go - Tree Node Conversion
// =============================================================================

func TestConvertTreeNodeToSerializable(t *testing.T) {
	// Create a simple decision node
	feature, err := models.NewFeature("test_feature", models.NumericFeature, 0)
	if err != nil {
		t.Fatalf("Failed to create feature: %v", err)
	}

	node, err := models.NewDecisionNode(feature, 5.0)
	if err != nil {
		t.Fatalf("Failed to create decision node: %v", err)
	}

	// Add test data
	node.Samples = 100
	node.Confidence = 0.8
	node.Impurity = 0.3
	node.ClassDistribution = map[interface{}]int{
		"class_a": 60,
		"class_b": 40,
	}

	// Create left and right children
	leftChild, err := models.NewLeafNode("left_class", map[interface{}]int{"left_class": 30}, 30)
	if err != nil {
		t.Fatalf("Failed to create left child: %v", err)
	}
	node.Left = leftChild

	rightChild, err := models.NewLeafNode("right_class", map[interface{}]int{"right_class": 70}, 70)
	if err != nil {
		t.Fatalf("Failed to create right child: %v", err)
	}
	node.Right = rightChild

	// Convert to serializable
	serializable := convertTreeNodeToSerializable(node)

	// Test basic properties
	if serializable.Type != string(node.Type) {
		t.Errorf("Expected type %s, got %s", node.Type, serializable.Type)
	}

	if serializable.Threshold != node.Threshold {
		t.Errorf("Expected threshold %f, got %f", node.Threshold, serializable.Threshold)
	}

	if serializable.Samples != node.Samples {
		t.Errorf("Expected samples %d, got %d", node.Samples, serializable.Samples)
	}

	// Test class distribution conversion
	if len(serializable.ClassDistribution) != 2 {
		t.Errorf("Expected 2 classes, got %d", len(serializable.ClassDistribution))
	}

	if serializable.ClassDistribution["class_a"] != 60 {
		t.Errorf("Expected class_a count 60, got %d", serializable.ClassDistribution["class_a"])
	}

	// Test children conversion
	if serializable.Left == nil {
		t.Error("Expected non-nil left child")
	}

	if serializable.Right == nil {
		t.Error("Expected non-nil right child")
	}
}

func TestConvertTreeNodeToSerializableWithCategories(t *testing.T) {
	// Create categorical decision node
	feature, err := models.NewFeature("cat_feature", models.CategoricalFeature, 0)
	if err != nil {
		t.Fatalf("Failed to create categorical feature: %v", err)
	}

	node, err := models.NewCategoricalDecisionNode(feature)
	if err != nil {
		t.Fatalf("Failed to create categorical decision node: %v", err)
	}

	// Add categories
	node.Categories = make(map[interface{}]*models.TreeNode)

	catChild1, err := models.NewLeafNode("cat1_class", map[interface{}]int{"cat1_class": 20}, 20)
	if err != nil {
		t.Fatalf("Failed to create category child 1: %v", err)
	}
	node.Categories["category1"] = catChild1

	catChild2, err := models.NewLeafNode("cat2_class", map[interface{}]int{"cat2_class": 30}, 30)
	if err != nil {
		t.Fatalf("Failed to create category child 2: %v", err)
	}
	node.Categories["category2"] = catChild2

	// Convert to serializable
	serializable := convertTreeNodeToSerializable(node)

	// Test categories conversion
	if len(serializable.Categories) != 2 {
		t.Errorf("Expected 2 categories, got %d", len(serializable.Categories))
	}

	if serializable.Categories["category1"] == nil {
		t.Error("Expected non-nil category1")
	}

	if serializable.Categories["category2"] == nil {
		t.Error("Expected non-nil category2")
	}
}

func TestConvertTreeNodeToSerializableNil(t *testing.T) {
	result := convertTreeNodeToSerializable(nil)
	if result != nil {
		t.Error("Expected nil result for nil input")
	}
}

func TestConvertFromSerializable(t *testing.T) {
	// Create serializable node
	serializable := &SerializableTreeNode{
		Type:       "leaf",
		Prediction: "test_class",
		Samples:    50,
		Confidence: 0.9,
		Impurity:   0.1,
		ClassDistribution: map[string]int{
			"test_class": 45,
			"other":      5,
		},
	}

	// Add children
	serializable.Left = &SerializableTreeNode{
		Type:              "leaf",
		Prediction:        "left_class",
		Samples:           25,
		ClassDistribution: map[string]int{"left_class": 25},
	}

	serializable.Right = &SerializableTreeNode{
		Type:              "leaf",
		Prediction:        "right_class",
		Samples:           25,
		ClassDistribution: map[string]int{"right_class": 25},
	}

	// Add categories
	serializable.Categories = map[string]*SerializableTreeNode{
		"cat1": {
			Type:              "leaf",
			Prediction:        "cat1_class",
			Samples:           10,
			ClassDistribution: map[string]int{"cat1_class": 10},
		},
	}

	// Convert back
	node := convertFromSerializable(serializable)

	// Test properties
	if string(node.Type) != serializable.Type {
		t.Errorf("Expected type %s, got %s", serializable.Type, node.Type)
	}

	if node.Prediction != serializable.Prediction {
		t.Errorf("Expected prediction %v, got %v", serializable.Prediction, node.Prediction)
	}

	if node.Samples != serializable.Samples {
		t.Errorf("Expected samples %d, got %d", serializable.Samples, node.Samples)
	}

	// Test class distribution
	if len(node.ClassDistribution) != 2 {
		t.Errorf("Expected 2 classes, got %d", len(node.ClassDistribution))
	}

	if node.ClassDistribution["test_class"] != 45 {
		t.Errorf("Expected test_class count 45, got %d", node.ClassDistribution["test_class"])
	}

	// Test children
	if node.Left == nil {
		t.Error("Expected non-nil left child")
	}

	if node.Right == nil {
		t.Error("Expected non-nil right child")
	}

	// Test categories
	if len(node.Categories) != 1 {
		t.Errorf("Expected 1 category, got %d", len(node.Categories))
	}

	if node.Categories["cat1"] == nil {
		t.Error("Expected non-nil cat1 category")
	}
}

func TestConvertFromSerializableNil(t *testing.T) {
	result := convertFromSerializable(nil)
	if result != nil {
		t.Error("Expected nil result for nil input")
	}
}

// =============================================================================
// Tests for serialization.go - Model Save/Load
// =============================================================================

func TestSaveModel(t *testing.T) {
	// Create temporary directory
	tempDir := t.TempDir()
	outputFile := filepath.Join(tempDir, "test_model.json")

	// Create simple decision tree
	tree, err := models.NewDecisionTree(models.CategoricalFeature, 5, 2)
	if err != nil {
		t.Fatalf("Failed to create decision tree: %v", err)
	}

	// Add feature
	_, err = tree.AddFeature("test_feature", models.NumericFeature)
	if err != nil {
		t.Fatalf("Failed to add feature: %v", err)
	}

	// Create root node
	root, err := models.NewLeafNode("test_class", map[interface{}]int{"test_class": 10}, 10)
	if err != nil {
		t.Fatalf("Failed to create leaf node: %v", err)
	}

	tree.Root = root
	tree.UpdateStatistics()

	// Create progress reporter
	progress := NewProgressReporter(false)

	// Test saving
	err = saveModel(tree, outputFile, progress)
	if err != nil {
		t.Fatalf("Failed to save model: %v", err)
	}

	// Verify file exists
	if _, err := os.Stat(outputFile); os.IsNotExist(err) {
		t.Error("Model file was not created")
	}

	// Verify file content
	data, err := os.ReadFile(outputFile)
	if err != nil {
		t.Fatalf("Failed to read saved model: %v", err)
	}

	var serializable SerializableDecisionTree
	err = json.Unmarshal(data, &serializable)
	if err != nil {
		t.Fatalf("Saved model is not valid JSON: %v", err)
	}

	// Verify properties
	if serializable.NodeCount != tree.NodeCount {
		t.Errorf("Expected node count %d, got %d", tree.NodeCount, serializable.NodeCount)
	}
}

func TestSaveModelDirectoryCreation(t *testing.T) {
	tempDir := t.TempDir()

	// Create output path with non-existent subdirectory
	outputFile := filepath.Join(tempDir, "subdir", "nested", "model.json")

	// Create minimal tree
	tree, err := models.NewDecisionTree(models.CategoricalFeature, 5, 2)
	if err != nil {
		t.Fatalf("Failed to create decision tree: %v", err)
	}

	progress := NewProgressReporter(false)

	// Test saving (should create directories)
	err = saveModel(tree, outputFile, progress)
	if err != nil {
		t.Fatalf("Failed to save model with directory creation: %v", err)
	}

	// Verify file exists
	if _, err := os.Stat(outputFile); os.IsNotExist(err) {
		t.Error("Model file was not created in nested directory")
	}
}

func TestLoadModel(t *testing.T) {
	// Create test model file
	tempDir := t.TempDir()
	modelFile := filepath.Join(tempDir, "test_model.json")

	// Create test data
	testModel := SerializableDecisionTree{
		TargetType: "categorical",
		NodeCount:  1,
		LeafCount:  1,
		Depth:      1,
		Features:   make(map[string]*models.Feature),
		Root: &SerializableTreeNode{
			Type:       "leaf",
			Prediction: "test_class",
			Samples:    10,
			Confidence: 1.0,
			Impurity:   0.0,
			ClassDistribution: map[string]int{
				"test_class": 10,
			},
		},
	}

	// Save test data
	data, err := json.MarshalIndent(testModel, "", "  ")
	if err != nil {
		t.Fatalf("Failed to marshal test data: %v", err)
	}

	err = os.WriteFile(modelFile, data, 0644)
	if err != nil {
		t.Fatalf("Failed to write test file: %v", err)
	}

	// Test loading
	loadedTree, err := loadModel(modelFile)
	if err != nil {
		t.Fatalf("Failed to load model: %v", err)
	}

	// Verify properties
	if loadedTree.NodeCount != testModel.NodeCount {
		t.Errorf("Expected node count %d, got %d", testModel.NodeCount, loadedTree.NodeCount)
	}

	if string(loadedTree.TargetType) != testModel.TargetType {
		t.Errorf("Expected target type %s, got %s", testModel.TargetType, loadedTree.TargetType)
	}

	if loadedTree.Root == nil {
		t.Error("Expected non-nil root node")
	}
}

func TestLoadModelErrors(t *testing.T) {
	// Test non-existent file
	_, err := loadModel("non_existent_file.json")
	if err == nil {
		t.Error("Expected error for non-existent file")
	}

	// Test invalid JSON
	tempDir := t.TempDir()
	invalidFile := filepath.Join(tempDir, "invalid.json")

	err = os.WriteFile(invalidFile, []byte("invalid json content"), 0644)
	if err != nil {
		t.Fatalf("Failed to write invalid JSON file: %v", err)
	}

	_, err = loadModel(invalidFile)
	if err == nil {
		t.Error("Expected error for invalid JSON")
	}
}

func TestConvertToSerializable(t *testing.T) {
	// Create decision tree
	tree, err := models.NewDecisionTree(models.CategoricalFeature, 5, 2)
	if err != nil {
		t.Fatalf("Failed to create decision tree: %v", err)
	}

	// Add feature
	_, err = tree.AddFeature("test_feature", models.NumericFeature)
	if err != nil {
		t.Fatalf("Failed to add feature: %v", err)
	}

	// Create root node
	root, err := models.NewLeafNode("test_class", map[interface{}]int{"test_class": 10}, 10)
	if err != nil {
		t.Fatalf("Failed to create leaf node: %v", err)
	}

	tree.Root = root
	tree.UpdateStatistics()

	// Test conversion
	serializable := convertToSerializable(tree)

	// Verify properties
	if serializable.NodeCount != tree.NodeCount {
		t.Errorf("Expected node count %d, got %d", tree.NodeCount, serializable.NodeCount)
	}

	if serializable.TargetType != string(tree.TargetType) {
		t.Errorf("Expected target type %s, got %s", tree.TargetType, serializable.TargetType)
	}

	if serializable.Root == nil {
		t.Error("Expected non-nil root in serializable tree")
	}
}

// =============================================================================
// Integration Tests for Higher Coverage
// =============================================================================

func TestCreateAndBuildTreeSuccess(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping integration test in short mode")
	}

	// Create test data
	features := [][]string{
		{"5.1", "3.5", "1.4", "0.2"},
		{"4.9", "3.0", "1.4", "0.2"},
		{"6.2", "2.8", "4.8", "1.8"},
		{"5.9", "3.0", "5.1", "1.8"},
	}
	targets := []string{"setosa", "setosa", "virginica", "virginica"}

	dataset := utils.NewCSVDataset(features, targets)

	// Create test features
	featureObjects := make([]*models.Feature, 4)
	for i := 0; i < 4; i++ {
		feature, err := models.NewFeature(fmt.Sprintf("feature_%d", i), models.NumericFeature, i)
		if err != nil {
			t.Fatalf("Failed to create feature %d: %v", i, err)
		}
		featureObjects[i] = feature
	}

	// Create test config
	config := &cli.Config{
		MaxDepth:        3,
		MinSamplesSplit: 2,
		MinSamplesLeaf:  1,
		Criterion:       "entropy",
		Verbose:         false,
	}

	// Test tree building
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	tree, err := createAndBuildTree(ctx, dataset, featureObjects, config)
	if err != nil {
		t.Fatalf("Failed to build tree: %v", err)
	}

	// Verify tree
	if tree == nil {
		t.Fatal("Expected non-nil tree")
	}

	if tree.Root == nil {
		t.Fatal("Expected non-nil root node")
	}
}

// Mock tests for functions that use external packages
func TestHandleTrainCommandWithMockedData(t *testing.T) {
	// This would require more complex mocking, but we can test error paths

	// Test with invalid config (this will fail at CLI validation)
	ctx := context.Background()
	invalidConfig := &cli.Config{
		InputFile:  "", // Invalid - empty input file
		TargetCol:  "",
		OutputFile: "",
	}

	err := handleTrainCommand(ctx, invalidConfig)
	if err == nil {
		t.Error("Expected error for invalid config")
	}
}

// Test edge cases for better coverage
func TestEmptyClassDistribution(t *testing.T) {
	// Test convertTreeNodeToSerializable with empty class distribution
	feature, err := models.NewFeature("test", models.NumericFeature, 0)
	if err != nil {
		t.Fatalf("Failed to create feature: %v", err)
	}

	node, err := models.NewDecisionNode(feature, 1.0)
	if err != nil {
		t.Fatalf("Failed to create node: %v", err)
	}

	node.ClassDistribution = make(map[interface{}]int) // Empty

	serializable := convertTreeNodeToSerializable(node)
	if len(serializable.ClassDistribution) != 0 {
		t.Errorf("Expected empty class distribution, got %d items", len(serializable.ClassDistribution))
	}
}

func TestEmptyCategories(t *testing.T) {
	// Test convertFromSerializable with empty categories
	serializable := &SerializableTreeNode{
		Type:              "decision",
		Categories:        make(map[string]*SerializableTreeNode), // Empty
		ClassDistribution: map[string]int{},
	}

	node := convertFromSerializable(serializable)
	if len(node.Categories) != 0 {
		t.Errorf("Expected empty categories, got %d items", len(node.Categories))
	}
}
