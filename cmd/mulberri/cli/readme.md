# CLI Implementation

This package implements a professional command-line interface for <PERSON><PERSON><PERSON><PERSON> using modern Go patterns and comprehensive error handling.

## Features

The CLI supports the following operations with extensive configuration options:

### Core Commands
- **Training models** (`train`) with configurable decision tree parameters
  - Specify target column with `-t <column_name>`
  - Input data from CSV files with `-i <file.csv>`
  - Save trained models with `-o <model.dt>`
  - Input metadata from YAML file with `-f <metadata.yaml>`
  - Configure tree depth with `--max-depth <number>`
  - Set minimum samples with `--min-samples <number>`
  - Choose splitting criterion with `--criterion <gini|entropy|mse>`

- **Running predictions** (`predict`) on new data
  - Use existing models with `-m <model.dt>`
  - Input new data with `-i <data.csv>`
  - Save predictions to CSV with `-o <predictions.csv>`

- **Utility commands**
  - Version information with `--version`
  - Help documentation with `--help`
  - Verbose output with `--verbose`

## Usage Examples

### Basic Training
```bash
mulberri -c train -i training_data.csv -t target_column -o model.dt
```

### Advanced Training with Custom Parameters
```bash
mulberri -c train -i training_data.csv -t species -o iris_model.dt \
  -f metadata.yaml --max-depth 5 --min-samples 10 --criterion entropy --verbose
```

### Making Predictions
```bash
mulberri -c predict -i new_data.csv -m model.dt -o predictions.csv --verbose
```

### Getting Help
```bash
mulberri --help
```

### Checking Version
```bash
mulberri --version
```

## Configuration Options

### Core Parameters
- `-c, --command`: Command to execute (`train` or `predict`) **[Required]**
- `-i, --input`: Input CSV file path **[Required]**
- `-o, --output`: Output file path **[Required]**
- `-t, --target`: Target column name (training only) **[Required for training]**
- `-m, --model`: Model file path (prediction only) **[Required for prediction]**
- `-f, --metadata`: Feature metadata YAML file path **[Optional]**
- `-log-config`: Logger configuration YAML file path **[Optional]**

### Decision Tree Parameters
- `--max-depth`: Maximum tree depth (default: 10)
- `--min-samples`: Minimum samples to split a node (default: 2)
- `--min-leaf`: Minimum samples at leaf node (default: 1)
- `--criterion`: Splitting criterion - `gini`, `entropy`, or `mse` (default: gini)

### CLI Options
- `--verbose`: Enable detailed output
- `--help`: Show comprehensive help
- `--version`: Display version information

## Error Handling

The CLI includes comprehensive error handling with structured error types:

### Input Validation
- File existence and accessibility checks
- File format validation (CSV for data, YAML for metadata)
- Path sanitization and security checks
- Parameter range validation

### Command Validation
- Required parameter verification for each command
- Cross-parameter consistency checks
- Decision tree parameter constraints

### Error Types
- **CLIError**: Structured errors with operation context, command info, and specific flag details
- **Validation Errors**: Parameter and file validation failures
- **Parsing Errors**: Command-line argument parsing issues

## Architecture

The CLI follows modern Go patterns with:

### Functional Options Pattern
```go
config, err := cli.NewConfig(
    cli.WithMaxDepth(5),
    cli.WithCriterion("entropy"),
    cli.WithVerbose(true),
)
```

### Structured Error Handling
```go
type CLIError struct {
    Op      string // Operation that failed
    Command string // Command being executed
    Flag    string // Specific flag (if applicable)
    Err     error  // Underlying error
}
```

### Comprehensive Validation
- Input file validation with format checking
- Parameter constraint validation
- Cross-parameter dependency validation
- Security checks for file paths

### Multiple Configuration Sources
- Command-line flags (highest precedence)
- Environment variables (medium precedence)
- Default values (lowest precedence)

## Environment Variables

For containerized deployments, configuration can be provided via environment variables:

```bash
export MULBERRI_COMMAND=train
export MULBERRI_INPUT_FILE=data.csv
export MULBERRI_TARGET_COL=target
export MULBERRI_OUTPUT_FILE=model.dt
export MULBERRI_MAX_DEPTH=5
export MULBERRI_CRITERION=entropy
export MULBERRI_VERBOSE=true
```

## Development

### Running Tests
```bash
# Run all tests with coverage
go test ./cmd/mulberri/cli/ -cover -race

# Run with verbose output
go test ./cmd/mulberri/cli/ -v

# Generate coverage report
go test ./cmd/mulberri/cli/ -coverprofile=coverage.out
go tool cover -html=coverage.out -o coverage.html
```

### Using Test Script
```bash
./scripts/run_tests.sh -c -u cmd/mulberri/cli/
```

### Code Quality
```bash
# Format code
go fmt ./cmd/mulberri/cli/

# Run linter
golangci-lint run ./cmd/mulberri/cli/

# Vet code
go vet ./cmd/mulberri/cli/
```

## Advanced Usage

### Programmatic Configuration
```go
// Create config with functional options
config, err := cli.NewConfig(
    cli.WithMaxDepth(10),
    cli.WithMinSamplesSplit(5),
    cli.WithCriterion("gini"),
)

// Parse from multiple sources
config, err := cli.GetConfigFromMultipleSources(
    cli.WithVerbose(true),
)
```

### Custom Validation
```go
config := &cli.Config{
    Command:   "train",
    InputFile: "data.csv",
    // ... other fields
}

if err := config.Validate(); err != nil {
    var cliErr *cli.CLIError
    if errors.As(err, &cliErr) {
        fmt.Printf("CLI Error in %s: %v\n", cliErr.Op, cliErr.Err)
    }
}
```

## Error Examples

```bash
# Missing required parameter
$ mulberri -c train -i data.csv
Error: CLI validation failed for command 'train' flag 'target': target column not specified for training (use -t <target_column>)

# Invalid file
$ mulberri -c train -i nonexistent.csv -t target -o model.dt
Error: CLI validation failed for command 'train' flag 'input': file does not exist: nonexistent.csv

# Invalid parameters
$ mulberri -c train -i data.csv -t target -o model.dt --max-depth 0
Error: CLI validation failed: max depth must be positive: 0

# Invalid file format
$ mulberri -c train -i data.txt -t target -o model.dt
Error: CLI validation failed for command 'train' flag 'input': invalid file extension: .txt (expected .csv)
```

## Integration Examples

### Docker Integration
```dockerfile
FROM golang:1.21-alpine AS builder
WORKDIR /app
COPY . .
RUN go build -o mulberri ./cmd/mulberri

FROM alpine:latest
RUN apk --no-cache add ca-certificates
WORKDIR /root/
COPY --from=builder /app/mulberri .

# Environment-based configuration
ENV MULBERRI_COMMAND=train
ENV MULBERRI_MAX_DEPTH=10
ENV MULBERRI_VERBOSE=true

CMD ["./mulberri"]
```

### CI/CD Pipeline
```yaml
name: Train Model
on: [push]
jobs:
  train:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Train Decision Tree
        run: |
          ./mulberri -c train \
            -i datasets/training.csv \
            -t target_column \
            -o models/production.dt \
            --max-depth 15 \
            --min-samples 5 \
            --criterion entropy \
            --verbose
```

### Batch Processing Script
```bash
#!/bin/bash
# Process multiple datasets with different configurations

datasets=("iris" "wine" "breast_cancer")
criterions=("gini" "entropy")

for dataset in "${datasets[@]}"; do
    for criterion in "${criterions[@]}"; do
        echo "Training ${dataset} with ${criterion}..."
        ./mulberri -c train \
            -i "data/${dataset}.csv" \
            -t "target" \
            -o "models/${dataset}_${criterion}.dt" \
            --criterion "${criterion}" \
            --max-depth 10 \
            --verbose
        
        if [ $? -eq 0 ]; then
            echo "✓ Successfully trained ${dataset} with ${criterion}"
        else
            echo "✗ Failed to train ${dataset} with ${criterion}"
        fi
    done
done
```

## Performance Considerations

### Memory Usage
- Efficient dataset handling with zero-copy operations where possible
- Configurable tree depth to control memory consumption
- Validation occurs before heavy processing to fail fast

### Validation Performance
- File existence checks are performed early
- Parameter validation is O(1) for most checks
- Path sanitization prevents security issues

### Error Performance
- Structured errors avoid expensive string formatting until needed
- Error context is preserved without unnecessary allocations
- Validation short-circuits on first error for quick feedback

## Best Practices

### Production Usage
1. **Always validate inputs**: Use the built-in validation or call `config.Validate()` explicitly
2. **Handle errors properly**: Check for `CLIError` types for structured error handling
3. **Use environment variables**: For containerized deployments
4. **Enable verbose mode**: For debugging and monitoring
5. **Set appropriate limits**: Configure `max-depth` and `min-samples` based on your data size

### Development
1. **Write comprehensive tests**: Cover both success and failure cases
2. **Use functional options**: For flexible configuration
3. **Validate early**: Catch configuration errors before processing
4. **Log appropriately**: Use verbose mode for debugging
5. **Handle edge cases**: Test with empty files, single records, etc.

## Troubleshooting

### Common Issues

#### File Not Found Errors
```bash
# Check file exists and is readable
ls -la your_file.csv
file your_file.csv
```

#### Permission Errors
```bash
# Ensure proper file permissions
chmod 644 input_file.csv
chmod 755 output_directory/
```

#### Memory Issues with Large Datasets
```bash
# Reduce tree complexity
mulberri -c train -i large_data.csv -t target -o model.dt \
  --max-depth 5 --min-samples 100
```

#### Parameter Validation Failures
```bash
# Check parameter ranges
mulberri --help  # Shows valid parameter ranges
```

### Debug Mode
```bash
# Enable verbose output for detailed logging
mulberri -c train -i data.csv -t target -o model.dt --verbose
```

## Contributing

When contributing to the CLI package:

1. **Follow Go conventions**: Use `gofmt`, `golint`, and `go vet`
2. **Add comprehensive tests**: Include both unit and integration tests
3. **Document new features**: Update this README and add code comments
4. **Handle errors properly**: Use the `CLIError` type for consistency
5. **Validate thoroughly**: Ensure all inputs are properly validated

### Test Coverage Requirements
- Minimum 80% coverage for new code
- All error paths must be tested
- Property-based tests for complex validation logic
- Integration tests for end-to-end workflows

## License

This CLI implementation is part of the Mulberri project and follows the same licensing terms.