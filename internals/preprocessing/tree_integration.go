package preprocessing

import (
	"context"
	"fmt"

	"github.com/berrijam/mulberri/internals/training"
	"github.com/berrijam/mulberri/internals/training/builder"
	"github.com/berrijam/mulberri/pkg/logger"
	"github.com/berrijam/mulberri/pkg/models"
)

// TreeBuilderWithDateTimePreprocessing provides a high-level interface for building trees with datetime preprocessing
type TreeBuilderWithDateTimePreprocessing struct {
	preprocessor *DateTimePreprocessor
	builder      *builder.TreeBuilder[string]
	config       *IntegrationConfig
}

// IntegrationConfig contains configuration for the integrated tree building process
type IntegrationConfig struct {
	// PreprocessorConfig contains datetime preprocessing options
	PreprocessorConfig *PreprocessorConfig
	// BuilderConfig contains tree building options
	BuilderConfig *builder.BuilderConfig
	// EnableLogging controls whether to log integration steps
	EnableLogging bool
}

// DefaultIntegrationConfig returns a default integration configuration
func DefaultIntegrationConfig() *IntegrationConfig {
	return &IntegrationConfig{
		PreprocessorConfig: DefaultPreprocessorConfig(),
		BuilderConfig:      nil, // Will use builder defaults
		EnableLogging:      false,
	}
}

// NewTreeBuilderWithDateTimePreprocessing creates a new integrated tree builder
func NewTreeBuilderWithDateTimePreprocessing(splitter *training.C45Splitter[string], config *IntegrationConfig) (*TreeBuilderWithDateTimePreprocessing, error) {
	if config == nil {
		config = DefaultIntegrationConfig()
	}

	// Create preprocessor
	preprocessor := NewDateTimePreprocessor(config.PreprocessorConfig)

	// Create tree builder with default configuration if none provided
	var treeBuilder *builder.TreeBuilder[string]
	var err error

	if config.BuilderConfig != nil {
		treeBuilder, err = builder.NewTreeBuilderWithConfig(splitter, config.BuilderConfig)
	} else {
		treeBuilder, err = builder.NewTreeBuilderWithInference(splitter)
	}

	if err != nil {
		return nil, fmt.Errorf("failed to create tree builder: %w", err)
	}

	return &TreeBuilderWithDateTimePreprocessing{
		preprocessor: preprocessor,
		builder:      treeBuilder,
		config:       config,
	}, nil
}

// BuildTreeWithDateTimePreprocessing builds a decision tree with automatic datetime preprocessing
func (tb *TreeBuilderWithDateTimePreprocessing) BuildTreeWithDateTimePreprocessing(
	ctx context.Context,
	data [][]string,
	targets []string,
	features []*models.Feature,
) (*models.DecisionTree, error) {

	if tb.config.EnableLogging {
		logger.Info("Starting tree building with datetime preprocessing")
		logger.Debug(fmt.Sprintf("Input: %d samples, %d features", len(data), len(features)))
	}

	// Step 1: Create preprocessed dataset
	dataset, err := NewPreprocessedDataset(data, targets, features, tb.preprocessor)
	if err != nil {
		return nil, fmt.Errorf("failed to create preprocessed dataset: %w", err)
	}

	// Validate the dataset
	if err := dataset.Validate(); err != nil {
		return nil, fmt.Errorf("dataset validation failed: %w", err)
	}

	// Step 2: Get processed features
	processedFeatures := dataset.GetProcessedFeatures()

	if tb.config.EnableLogging {
		logger.Debug(fmt.Sprintf("Preprocessing complete: %d processed features", len(processedFeatures)))
		
		// Log feature transformations
		for _, feature := range processedFeatures {
			if feature.Type == models.NumericFeature && (feature.ColumnNumber < 0 || feature.Name != features[feature.ColumnNumber].Name) {
				logger.Debug(fmt.Sprintf("Transformed feature: %s (type: %s, column: %d)", 
					feature.Name, feature.Type, feature.ColumnNumber))
			}
		}
	}

	// Step 3: Build the tree using the preprocessed dataset
	tree, err := tb.builder.BuildTree(ctx, dataset, processedFeatures)
	if err != nil {
		return nil, fmt.Errorf("tree building failed: %w", err)
	}

	if tb.config.EnableLogging {
		logger.Info("Tree building with datetime preprocessing completed successfully")
	}

	return tree, nil
}

// PreprocessFeaturesOnly performs only the feature preprocessing step without building a tree
func (tb *TreeBuilderWithDateTimePreprocessing) PreprocessFeaturesOnly(
	data [][]string,
	features []*models.Feature,
) ([]*models.Feature, error) {

	if tb.config.EnableLogging {
		logger.Info("Preprocessing features only")
	}

	processedFeatures, err := tb.preprocessor.PreprocessDateTimeFeatures(features, data)
	if err != nil {
		return nil, fmt.Errorf("feature preprocessing failed: %w", err)
	}

	if tb.config.EnableLogging {
		logger.Info(fmt.Sprintf("Feature preprocessing completed: %d -> %d features", 
			len(features), len(processedFeatures)))
	}

	return processedFeatures, nil
}

// AnalyzeDateTimeFeatures analyzes the dataset to identify datetime features and their characteristics
func (tb *TreeBuilderWithDateTimePreprocessing) AnalyzeDateTimeFeatures(
	data [][]string,
	features []*models.Feature,
) (*DateTimeAnalysis, error) {

	analysis := &DateTimeAnalysis{
		TotalFeatures:    len(features),
		DateTimeFeatures: make([]*DateTimeFeatureInfo, 0),
		Recommendations:  make([]string, 0),
	}

	for _, feature := range features {
		if feature.Type == models.DateFeature {
			info, err := tb.analyzeIndividualDateTimeFeature(data, feature)
			if err != nil {
				return nil, fmt.Errorf("failed to analyze feature %s: %w", feature.Name, err)
			}
			analysis.DateTimeFeatures = append(analysis.DateTimeFeatures, info)
		}
	}

	// Generate recommendations
	analysis.Recommendations = tb.generateRecommendations(analysis)

	return analysis, nil
}

// DateTimeAnalysis contains analysis results for datetime features
type DateTimeAnalysis struct {
	TotalFeatures    int
	DateTimeFeatures []*DateTimeFeatureInfo
	Recommendations  []string
}

// DateTimeFeatureInfo contains information about a specific datetime feature
type DateTimeFeatureInfo struct {
	FeatureName     string
	ColumnNumber    int
	SampleCount     int
	ValidSamples    int
	InvalidSamples  int
	EmptySamples    int
	DateRange       *DateRange
	Formats         []string
	SuggestedSplit  bool
}

// DateRange represents the range of dates in a feature
type DateRange struct {
	Earliest string
	Latest   string
	Span     string
}

// analyzeIndividualDateTimeFeature analyzes a single datetime feature
func (tb *TreeBuilderWithDateTimePreprocessing) analyzeIndividualDateTimeFeature(
	data [][]string,
	feature *models.Feature,
) (*DateTimeFeatureInfo, error) {

	info := &DateTimeFeatureInfo{
		FeatureName:  feature.Name,
		ColumnNumber: feature.ColumnNumber,
		SampleCount:  len(data),
		Formats:      make([]string, 0),
	}

	// Analyze sample values
	formatCounts := make(map[string]int)
	
	for i, row := range data {
		if feature.ColumnNumber >= len(row) {
			continue
		}

		value := row[feature.ColumnNumber]
		if value == "" {
			info.EmptySamples++
			continue
		}

		// Try to convert the value
		_, err := tb.preprocessor.ConvertDateTimeValue(value)
		if err != nil {
			info.InvalidSamples++
		} else {
			info.ValidSamples++
			
			// Detect format
			format := tb.detectDateTimeFormat(value)
			formatCounts[format]++
		}

		// Stop after analyzing first 100 samples for performance
		if i >= 100 {
			break
		}
	}

	// Extract unique formats
	for format := range formatCounts {
		info.Formats = append(info.Formats, format)
	}

	// Suggest splitting if the feature has good coverage and variety
	info.SuggestedSplit = info.ValidSamples > info.SampleCount/2 && len(info.Formats) > 0

	return info, nil
}

// detectDateTimeFormat attempts to detect the format of a datetime string
func (tb *TreeBuilderWithDateTimePreprocessing) detectDateTimeFormat(value string) string {
	// Simple format detection based on common patterns
	if len(value) == 10 && value[4] == '-' && value[7] == '-' {
		return "YYYY-MM-DD"
	}
	if len(value) == 19 && value[4] == '-' && value[7] == '-' && value[10] == ' ' {
		return "YYYY-MM-DD HH:MM:SS"
	}
	if len(value) >= 19 && value[4] == '-' && value[7] == '-' && value[10] == 'T' {
		return "ISO8601"
	}
	return "Unknown"
}

// generateRecommendations generates preprocessing recommendations based on analysis
func (tb *TreeBuilderWithDateTimePreprocessing) generateRecommendations(analysis *DateTimeAnalysis) []string {
	var recommendations []string

	if len(analysis.DateTimeFeatures) == 0 {
		recommendations = append(recommendations, "No datetime features detected")
		return recommendations
	}

	for _, feature := range analysis.DateTimeFeatures {
		if feature.InvalidSamples > feature.ValidSamples {
			recommendations = append(recommendations, 
				fmt.Sprintf("Feature '%s' has many invalid datetime values (%d/%d) - consider data cleaning", 
					feature.FeatureName, feature.InvalidSamples, feature.SampleCount))
		}

		if feature.SuggestedSplit {
			recommendations = append(recommendations, 
				fmt.Sprintf("Feature '%s' is a good candidate for component splitting", feature.FeatureName))
		}

		if feature.EmptySamples > feature.SampleCount/4 {
			recommendations = append(recommendations, 
				fmt.Sprintf("Feature '%s' has many empty values (%d/%d) - consider handling missing data", 
					feature.FeatureName, feature.EmptySamples, feature.SampleCount))
		}
	}

	return recommendations
}

// GetPreprocessor returns the datetime preprocessor for direct access
func (tb *TreeBuilderWithDateTimePreprocessing) GetPreprocessor() *DateTimePreprocessor {
	return tb.preprocessor
}

// GetBuilder returns the tree builder for direct access
func (tb *TreeBuilderWithDateTimePreprocessing) GetBuilder() *builder.TreeBuilder[string] {
	return tb.builder
}
