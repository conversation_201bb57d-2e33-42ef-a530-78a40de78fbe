package datetimeconverter

import (
	"fmt"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestNewDateTimeConverter(t *testing.T) {
	converter := NewDateTimeConverter()
	assert.NotNil(t, converter)
	assert.NotNil(t, converter.dateOnlyRegex)
	assert.NotNil(t, converter.timeOnlyRegex)
	assert.NotNil(t, converter.dateTimeRegex)
}

func TestConvertISO8601ToFloat64_DateOnly(t *testing.T) {
	converter := NewDateTimeConverter()

	tests := []struct {
		name     string
		input    string
		expected float64
		hasError bool
	}{
		{
			name:     "valid date",
			input:    "2023-12-25",
			expected: 20231225,
			hasError: false,
		},
		{
			name:     "valid date with leading zeros",
			input:    "2023-01-05",
			expected: 20230105,
			hasError: false,
		},
		{
			name:     "leap year date",
			input:    "2024-02-29",
			expected: 20240229,
			hasError: false,
		},
		{
			name:     "invalid date format",
			input:    "2023/12/25",
			expected: 0,
			hasError: true,
		},
		{
			name:     "invalid date - too short",
			input:    "23-12-25",
			expected: 0,
			hasError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := converter.ConvertISO8601ToFloat64(tt.input)
			if tt.hasError {
				assert.Error(t, err)
				assert.IsType(t, &DateTimeConversionError{}, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expected, result)
			}
		})
	}
}

func TestConvertISO8601ToFloat64_TimeOnly(t *testing.T) {
	converter := NewDateTimeConverter()

	tests := []struct {
		name     string
		input    string
		expected float64
		hasError bool
	}{
		{
			name:     "time with milliseconds and Z",
			input:    "14:30:45.123Z",
			expected: 143045,
			hasError: false,
		},
		{
			name:     "time without milliseconds and Z",
			input:    "14:30:45Z",
			expected: 143045,
			hasError: false,
		},
		{
			name:     "time with timezone offset",
			input:    "14:30:45.123+05:30",
			expected: 90045, // Converted to UTC (14:30 - 5:30 = 09:00)
			hasError: false,
		},
		{
			name:     "time with negative timezone offset",
			input:    "14:30:45.123-08:00",
			expected: 223045, // Converted to UTC (14:30 + 8:00 = 22:30)
			hasError: false,
		},
		{
			name:     "midnight time",
			input:    "00:00:00Z",
			expected: 0,
			hasError: false,
		},
		{
			name:     "end of day time",
			input:    "23:59:59Z",
			expected: 235959,
			hasError: false,
		},
		{
			name:     "invalid time format - no timezone",
			input:    "14:30:45",
			expected: 0,
			hasError: true,
		},
		{
			name:     "invalid time format - wrong separator",
			input:    "14.30.45Z",
			expected: 0,
			hasError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := converter.ConvertISO8601ToFloat64(tt.input)
			if tt.hasError {
				assert.Error(t, err)
				assert.IsType(t, &DateTimeConversionError{}, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expected, result)
			}
		})
	}
}

func TestConvertISO8601ToFloat64_DateTime(t *testing.T) {
	converter := NewDateTimeConverter()

	tests := []struct {
		name     string
		input    string
		expected float64
		hasError bool
	}{
		{
			name:     "full datetime with milliseconds and Z",
			input:    "2023-12-25T14:30:45.123Z",
			expected: 20231225143045,
			hasError: false,
		},
		{
			name:     "full datetime without milliseconds and Z",
			input:    "2023-12-25T14:30:45Z",
			expected: 20231225143045,
			hasError: false,
		},
		{
			name:     "datetime with timezone offset",
			input:    "2023-12-25T14:30:45.123+05:30",
			expected: 20231225090045, // Converted to UTC (14:30 - 5:30 = 09:00)
			hasError: false,
		},
		{
			name:     "datetime with negative timezone offset",
			input:    "2023-12-25T14:30:45.123-08:00",
			expected: 20231225223045, // Converted to UTC (14:30 + 8:00 = 22:30)
			hasError: false,
		},
		{
			name:     "new year datetime",
			input:    "2024-01-01T00:00:00Z",
			expected: 20240101000000,
			hasError: false,
		},
		{
			name:     "invalid datetime - missing T separator",
			input:    "2023-12-25 14:30:45Z",
			expected: 0,
			hasError: true,
		},
		{
			name:     "invalid datetime - wrong date format",
			input:    "2023/12/25T14:30:45Z",
			expected: 0,
			hasError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := converter.ConvertISO8601ToFloat64(tt.input)
			if tt.hasError {
				assert.Error(t, err)
				assert.IsType(t, &DateTimeConversionError{}, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expected, result)
			}
		})
	}
}

func TestConvertISO8601ToFloat64_EdgeCases(t *testing.T) {
	converter := NewDateTimeConverter()

	tests := []struct {
		name     string
		input    string
		expected float64
		hasError bool
	}{
		{
			name:     "empty string",
			input:    "",
			expected: 0,
			hasError: true,
		},
		{
			name:     "whitespace only",
			input:    "   ",
			expected: 0,
			hasError: true,
		},
		{
			name:     "string with leading/trailing whitespace",
			input:    "  2023-12-25  ",
			expected: 20231225,
			hasError: false,
		},
		{
			name:     "invalid format",
			input:    "not-a-date",
			expected: 0,
			hasError: true,
		},
		{
			name:     "partial date",
			input:    "2023-12",
			expected: 0,
			hasError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := converter.ConvertISO8601ToFloat64(tt.input)
			if tt.hasError {
				assert.Error(t, err)
				assert.IsType(t, &DateTimeConversionError{}, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expected, result)
			}
		})
	}
}

func TestDateTimeConversionError(t *testing.T) {
	// Test error without wrapped error
	err1 := &DateTimeConversionError{
		Input:  "invalid-input",
		Reason: "test reason",
	}
	assert.Contains(t, err1.Error(), "invalid-input")
	assert.Contains(t, err1.Error(), "test reason")
	assert.Nil(t, err1.Unwrap())

	// Test error with wrapped error
	wrappedErr := assert.AnError
	err2 := &DateTimeConversionError{
		Input:  "invalid-input",
		Reason: "test reason",
		Err:    wrappedErr,
	}
	assert.Contains(t, err2.Error(), "invalid-input")
	assert.Contains(t, err2.Error(), "test reason")
	assert.Equal(t, wrappedErr, err2.Unwrap())
}

// Benchmark tests
func BenchmarkConvertISO8601ToFloat64_Date(b *testing.B) {
	converter := NewDateTimeConverter()
	input := "2023-12-25"

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, _ = converter.ConvertISO8601ToFloat64(input)
	}
}

func BenchmarkConvertISO8601ToFloat64_Time(b *testing.B) {
	converter := NewDateTimeConverter()
	input := "14:30:45.123Z"

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, _ = converter.ConvertISO8601ToFloat64(input)
	}
}

func BenchmarkConvertISO8601ToFloat64_DateTime(b *testing.B) {
	converter := NewDateTimeConverter()
	input := "2023-12-25T14:30:45.123Z"

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, _ = converter.ConvertISO8601ToFloat64(input)
	}
}

// Additional tests to improve coverage
func TestConvertISO8601ToFloat64_ErrorPaths(t *testing.T) {
	converter := NewDateTimeConverter()

	tests := []struct {
		name        string
		input       string
		expectError bool
		description string
	}{
		{
			name:        "invalid_datetime_parsing",
			input:       "2023-13-45T25:70:90Z", // Invalid date and time components
			expectError: true,
			description: "Should fail on invalid datetime components",
		},
		{
			name:        "invalid_date_parsing",
			input:       "2023-02-30", // Invalid date (Feb 30th doesn't exist)
			expectError: true,
			description: "Should fail on invalid date",
		},
		{
			name:        "malformed_timezone",
			input:       "14:30:45+25:70", // Invalid timezone offset - but regex allows it
			expectError: false,            // The regex validation passes, but parsing might handle it
			description: "Malformed timezone might be handled by the parser",
		},
		{
			name:        "whitespace_input",
			input:       "   2023-12-25   ", // Input with whitespace
			expectError: false,
			description: "Should handle whitespace correctly",
		},
		{
			name:        "only_whitespace",
			input:       "   ", // Only whitespace
			expectError: true,
			description: "Should fail on whitespace-only input",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := converter.ConvertISO8601ToFloat64(tt.input)

			if tt.expectError {
				assert.Error(t, err, tt.description)
				assert.Equal(t, float64(0), result, "Should return 0 on error")
				assert.IsType(t, &DateTimeConversionError{}, err, "Should return DateTimeConversionError")
			} else {
				assert.NoError(t, err, tt.description)
				assert.Greater(t, result, float64(0), "Should return positive result on success")
			}
		})
	}
}

func TestConvertISO8601ToFloat64_EdgeCasesExtended(t *testing.T) {
	converter := NewDateTimeConverter()

	tests := []struct {
		name     string
		input    string
		expected float64
		hasError bool
	}{
		{
			name:     "leap_year_valid",
			input:    "2024-02-29",
			expected: 20240229,
			hasError: false,
		},
		{
			name:     "leap_year_invalid",
			input:    "2023-02-29", // 2023 is not a leap year
			expected: 0,
			hasError: true,
		},
		{
			name:     "year_boundary",
			input:    "1999-12-31T23:59:59Z",
			expected: 19991231235959,
			hasError: false,
		},
		{
			name:     "millennium_boundary",
			input:    "2000-01-01T00:00:00Z",
			expected: 20000101000000,
			hasError: false,
		},
		{
			name:     "far_future_date",
			input:    "2099-12-31",
			expected: 20991231,
			hasError: false,
		},
		{
			name:     "time_with_large_offset",
			input:    "12:00:00+12:00", // UTC-12 to UTC+12 range
			expected: 0,                // 12:00 - 12:00 = 00:00
			hasError: false,
		},
		{
			name:     "time_with_negative_large_offset",
			input:    "12:00:00-12:00", // UTC-12 to UTC+12 range
			expected: 0,                // 12:00 + 12:00 = 24:00, but wraps to 00:00 for time-only
			hasError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := converter.ConvertISO8601ToFloat64(tt.input)
			if tt.hasError {
				assert.Error(t, err)
				assert.Equal(t, float64(0), result)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expected, result)
			}
		})
	}
}

func TestDateTimeConversionError_Coverage(t *testing.T) {
	// Test error without wrapped error
	err1 := &DateTimeConversionError{
		Input:  "test-input",
		Reason: "test reason",
	}

	errorMsg := err1.Error()
	assert.Contains(t, errorMsg, "test-input")
	assert.Contains(t, errorMsg, "test reason")
	assert.Nil(t, err1.Unwrap())

	// Test error with wrapped error
	wrappedErr := fmt.Errorf("wrapped error")
	err2 := &DateTimeConversionError{
		Input:  "test-input",
		Reason: "test reason",
		Err:    wrappedErr,
	}

	errorMsg2 := err2.Error()
	assert.Contains(t, errorMsg2, "test-input")
	assert.Contains(t, errorMsg2, "test reason")
	assert.Contains(t, errorMsg2, "wrapped error")
	assert.Equal(t, wrappedErr, err2.Unwrap())
}

// TestFloat64PrecisionLoss tests for precision loss when converting 14-digit integers to float64
// This is critical because Go's float64 has 53 bits of precision, and large integers may lose accuracy
func TestFloat64PrecisionLoss(t *testing.T) {
	converter := NewDateTimeConverter()

	tests := []struct {
		name                string
		input               string
		expectedFloat64     float64
		description         string
		expectPrecisionLoss bool
	}{
		{
			name:                "small_datetime_no_loss",
			input:               "1970-01-01T00:00:00Z",
			expectedFloat64:     19700101000000,
			description:         "Small datetime should have no precision loss",
			expectPrecisionLoss: false,
		},
		{
			name:                "typical_datetime_no_loss",
			input:               "2023-12-25T14:30:45Z",
			expectedFloat64:     20231225143045,
			description:         "Typical datetime should have no precision loss",
			expectPrecisionLoss: false,
		},
		{
			name:                "large_datetime_potential_loss",
			input:               "2099-12-31T23:59:59Z",
			expectedFloat64:     20991231235959,
			description:         "Large datetime might have precision loss",
			expectPrecisionLoss: false, // This is still within safe range
		},
		{
			name:                "edge_case_datetime",
			input:               "9999-12-31T23:59:59Z",
			expectedFloat64:     99991231235959,
			description:         "Very large datetime at edge of 14-digit range",
			expectPrecisionLoss: false, // Actually still within safe range for 14-digit datetimes
		},
	}

	// IEEE 754 double precision (float64) has 53 bits of precision
	// This means integers larger than 2^53 (9,007,199,254,740,992) may lose precision
	const maxSafeInteger = int64(1 << 53) // 9,007,199,254,740,992

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Convert using our datetime converter
			result, err := converter.ConvertISO8601ToFloat64(tt.input)
			assert.NoError(t, err, "Conversion should succeed")
			assert.Equal(t, tt.expectedFloat64, result, "Should get expected float64 result")

			// Convert the float64 result to int64 and back to test precision loss
			asInt := int64(result)
			backToFloat := float64(asInt)

			// Check if we're in the potentially problematic range
			maxSafeFloat := float64(maxSafeInteger)
			if result > maxSafeFloat {
				t.Logf("WARNING: Float64 %f exceeds safe integer precision range (%f)", result, maxSafeFloat)
			}

			// Check for precision loss
			if result != backToFloat {
				if tt.expectPrecisionLoss {
					t.Logf("Expected precision loss detected: %f -> %d -> %f", result, asInt, backToFloat)
					assert.NotEqual(t, result, backToFloat, "Expected precision loss for large numbers")
				} else {
					t.Errorf("Unexpected precision loss: %f -> %d -> %f", result, asInt, backToFloat)
					assert.Equal(t, result, backToFloat, "Should not lose precision for this range")
				}
			} else {
				if tt.expectPrecisionLoss {
					t.Logf("No precision loss detected (unexpected): %f -> %d -> %f", result, asInt, backToFloat)
				} else {
					t.Logf("No precision loss (as expected): %f -> %d -> %f", result, asInt, backToFloat)
				}
				assert.Equal(t, result, backToFloat, "Round-trip conversion should be exact")
			}

			// Additional checks for precision representation
			t.Logf("Original float64: %.0f", result)
			t.Logf("As int64: %d", asInt)
			t.Logf("Back to float64: %.0f", backToFloat)
			t.Logf("Difference: %.0f", result-backToFloat)
		})
	}

	// Test the boundary cases more explicitly
	t.Run("precision_boundary_analysis", func(t *testing.T) {
		// Test values around the IEEE 754 precision boundary
		testValues := []int64{
			9007199254740991, // 2^53 - 1 (last exactly representable integer)
			9007199254740992, // 2^53 (still exactly representable)
			9007199254740993, // 2^53 + 1 (first integer that may lose precision)
			20231225143045,   // Typical datetime value
			99991231235959,   // Maximum 14-digit datetime value
		}

		for _, val := range testValues {
			asFloat := float64(val)
			backToInt := int64(asFloat)

			if val != backToInt {
				t.Logf("PRECISION LOSS: %d -> %f -> %d (diff: %d)", val, asFloat, backToInt, val-backToInt)
			} else {
				t.Logf("NO LOSS: %d -> %f -> %d", val, asFloat, backToInt)
			}
		}
	})
}

// Tests for the new component splitting functionality
func TestConvertISO8601ToComponents(t *testing.T) {
	converter := NewDateTimeConverter()

	tests := []struct {
		name     string
		input    string
		expected DateTimeComponents
		hasError bool
	}{
		{
			name:  "full datetime with timezone",
			input: "2023-12-25T14:30:45.123Z",
			expected: DateTimeComponents{
				Year: 2023, Month: 12, Day: 25,
				Hour: 14, Minute: 30, Second: 45,
			},
			hasError: false,
		},
		{
			name:  "date only",
			input: "2023-12-25",
			expected: DateTimeComponents{
				Year: 2023, Month: 12, Day: 25,
				Hour: 0, Minute: 0, Second: 0,
			},
			hasError: false,
		},
		{
			name:  "time only with UTC",
			input: "14:30:45Z",
			expected: DateTimeComponents{
				Year: 1970, Month: 1, Day: 1,
				Hour: 14, Minute: 30, Second: 45,
			},
			hasError: false,
		},
		{
			name:  "time with positive timezone offset",
			input: "14:30:45+05:30",
			expected: DateTimeComponents{
				Year: 1970, Month: 1, Day: 1,
				Hour: 9, Minute: 0, Second: 45, // 14:30 - 5:30 = 09:00
			},
			hasError: false,
		},
		{
			name:  "time with negative timezone offset",
			input: "14:30:45-08:00",
			expected: DateTimeComponents{
				Year: 1970, Month: 1, Day: 1,
				Hour: 22, Minute: 30, Second: 45, // 14:30 + 8:00 = 22:30
			},
			hasError: false,
		},
		{
			name:     "empty input",
			input:    "",
			expected: DateTimeComponents{},
			hasError: true,
		},
		{
			name:     "invalid format",
			input:    "invalid-datetime",
			expected: DateTimeComponents{},
			hasError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := converter.ConvertISO8601ToComponents(tt.input)
			if tt.hasError {
				assert.Error(t, err)
				assert.IsType(t, &DateTimeConversionError{}, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expected, result)
			}
		})
	}
}

func TestConvertISO8601ToSeparateIntegers(t *testing.T) {
	converter := NewDateTimeConverter()

	tests := []struct {
		name                                      string
		input                                     string
		expectedYear, expectedMonth, expectedDay  int
		expectedHour, expectedMinute, expectedSec int
		hasError                                  bool
	}{
		{
			name:           "full datetime",
			input:          "2023-12-25T14:30:45Z",
			expectedYear:   2023,
			expectedMonth:  12,
			expectedDay:    25,
			expectedHour:   14,
			expectedMinute: 30,
			expectedSec:    45,
			hasError:       false,
		},
		{
			name:           "date only",
			input:          "2023-12-25",
			expectedYear:   2023,
			expectedMonth:  12,
			expectedDay:    25,
			expectedHour:   0,
			expectedMinute: 0,
			expectedSec:    0,
			hasError:       false,
		},
		{
			name:           "time only",
			input:          "14:30:45Z",
			expectedYear:   1970,
			expectedMonth:  1,
			expectedDay:    1,
			expectedHour:   14,
			expectedMinute: 30,
			expectedSec:    45,
			hasError:       false,
		},
		{
			name:           "invalid input",
			input:          "invalid",
			expectedYear:   0,
			expectedMonth:  0,
			expectedDay:    0,
			expectedHour:   0,
			expectedMinute: 0,
			expectedSec:    0,
			hasError:       true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			year, month, day, hour, minute, second, err := converter.ConvertISO8601ToSeparateIntegers(tt.input)
			if tt.hasError {
				assert.Error(t, err)
				assert.IsType(t, &DateTimeConversionError{}, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expectedYear, year)
				assert.Equal(t, tt.expectedMonth, month)
				assert.Equal(t, tt.expectedDay, day)
				assert.Equal(t, tt.expectedHour, hour)
				assert.Equal(t, tt.expectedMinute, minute)
				assert.Equal(t, tt.expectedSec, second)
			}
		})
	}
}

func TestExtractIndividualComponents(t *testing.T) {
	converter := NewDateTimeConverter()

	// Test data
	testInput := "2023-12-25T14:30:45Z"
	invalidInput := "invalid-datetime"

	t.Run("ExtractYear", func(t *testing.T) {
		year, err := converter.ExtractYear(testInput)
		assert.NoError(t, err)
		assert.Equal(t, 2023, year)

		_, err = converter.ExtractYear(invalidInput)
		assert.Error(t, err)
	})

	t.Run("ExtractMonth", func(t *testing.T) {
		month, err := converter.ExtractMonth(testInput)
		assert.NoError(t, err)
		assert.Equal(t, 12, month)

		_, err = converter.ExtractMonth(invalidInput)
		assert.Error(t, err)
	})

	t.Run("ExtractDay", func(t *testing.T) {
		day, err := converter.ExtractDay(testInput)
		assert.NoError(t, err)
		assert.Equal(t, 25, day)

		_, err = converter.ExtractDay(invalidInput)
		assert.Error(t, err)
	})

	t.Run("ExtractHour", func(t *testing.T) {
		hour, err := converter.ExtractHour(testInput)
		assert.NoError(t, err)
		assert.Equal(t, 14, hour)

		_, err = converter.ExtractHour(invalidInput)
		assert.Error(t, err)
	})

	t.Run("ExtractMinute", func(t *testing.T) {
		minute, err := converter.ExtractMinute(testInput)
		assert.NoError(t, err)
		assert.Equal(t, 30, minute)

		_, err = converter.ExtractMinute(invalidInput)
		assert.Error(t, err)
	})

	t.Run("ExtractSecond", func(t *testing.T) {
		second, err := converter.ExtractSecond(testInput)
		assert.NoError(t, err)
		assert.Equal(t, 45, second)

		_, err = converter.ExtractSecond(invalidInput)
		assert.Error(t, err)
	})

	// Test with time-only input
	timeOnlyInput := "14:30:45Z"
	t.Run("ExtractFromTimeOnly", func(t *testing.T) {
		year, err := converter.ExtractYear(timeOnlyInput)
		assert.NoError(t, err)
		assert.Equal(t, 1970, year) // Default year for time-only

		hour, err := converter.ExtractHour(timeOnlyInput)
		assert.NoError(t, err)
		assert.Equal(t, 14, hour)
	})

	// Test with date-only input
	dateOnlyInput := "2023-12-25"
	t.Run("ExtractFromDateOnly", func(t *testing.T) {
		year, err := converter.ExtractYear(dateOnlyInput)
		assert.NoError(t, err)
		assert.Equal(t, 2023, year)

		hour, err := converter.ExtractHour(dateOnlyInput)
		assert.NoError(t, err)
		assert.Equal(t, 0, hour) // Default hour for date-only
	})
}
