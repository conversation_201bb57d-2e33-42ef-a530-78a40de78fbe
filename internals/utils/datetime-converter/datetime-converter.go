// Package utils provides utilities for converting ISO 8601 formatted date/time strings
// into numeric formats for data processing.
//
// This utility focuses on converting ISO 8601 formatted strings in the format:
// YYYY-MM-DDTHH:mm:ss.sssZ (where T separates date and time, Z indicates UTC or offset)
//
// Conversion rules:
// - Date only: YYYYMMDD (8-digit number)
// - Time only: HHMMSS (6-digit number, milliseconds ignored)
// - Date and time: YYYYMMDDHHMMSS (14-digit number)
//
// Example usage:
//
//	converter := utils.NewDateTimeConverter()
//
//	// Convert date only
//	result, err := converter.ConvertISO8601("2023-12-25")
//	// result: 20231225
//
//	// Convert time only
//	result, err := converter.ConvertISO8601("14:30:45.123Z")
//	// result: 143045
//
//	// Convert date and time
//	result, err := converter.ConvertISO8601("2023-12-25T14:30:45.123Z")
//	// result: 20231225143045
package datetimeconverter

import (
	"fmt"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/relvacode/iso8601"
)

// DateTimeConverter handles conversion of ISO 8601 formatted strings to numeric formats
type DateTimeConverter struct {
	// dateOnlyRegex matches date-only patterns like "2023-12-25"
	dateOnlyRegex *regexp.Regexp
	// timeOnlyRegex matches time-only patterns like "14:30:45.123Z" or "14:30:45Z"
	timeOnlyRegex *regexp.Regexp
	// dateTimeRegex matches full datetime patterns like "2023-12-25T14:30:45.123Z"
	dateTimeRegex *regexp.Regexp
}

// DateTimeConversionError represents errors that occur during date/time conversion
type DateTimeConversionError struct {
	Input  string
	Reason string
	Err    error
}

func (e *DateTimeConversionError) Error() string {
	if e.Err != nil {
		return fmt.Sprintf("datetime conversion failed for input '%s': %s (%v)", e.Input, e.Reason, e.Err)
	}
	return fmt.Sprintf("datetime conversion failed for input '%s': %s", e.Input, e.Reason)
}

func (e *DateTimeConversionError) Unwrap() error {
	return e.Err
}

// NewDateTimeConverter creates a new DateTimeConverter with compiled regex patterns
func NewDateTimeConverter() *DateTimeConverter {
	return &DateTimeConverter{
		// Date only: YYYY-MM-DD
		dateOnlyRegex: regexp.MustCompile(`^\d{4}-\d{2}-\d{2}$`),
		// Time only: HH:mm:ss.sssZ or HH:mm:ssZ or HH:mm:ss.sss+/-HH:MM
		timeOnlyRegex: regexp.MustCompile(`^\d{2}:\d{2}:\d{2}(\.\d{3})?(Z|[+-]\d{2}:\d{2})$`),
		// DateTime: YYYY-MM-DDTHH:mm:ss.sssZ or similar with timezone
		dateTimeRegex: regexp.MustCompile(`^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(\.\d{3})?(Z|[+-]\d{2}:\d{2})$`),
	}
}

// ConvertISO8601Toint64 converts an ISO 8601 formatted string to the appropriate numeric format as int64
//
// Parameters:
// - input: ISO 8601 formatted string (date, time, or datetime)
//
// Returns:
// - int64: The converted numeric value
// - error: Any error encountered during conversion
//
// Conversion rules:
// - Date only (YYYY-MM-DD): returns YYYYMMDD (8-digit number)
// - Time only (HH:mm:ss.sssZ): returns HHMMSS (6-digit number, ignoring milliseconds)
// - DateTime (YYYY-MM-DDTHH:mm:ss.sssZ): returns YYYYMMDDHHMMSS (14-digit number)
func (dtc *DateTimeConverter) ConvertISO8601Toint64(input string) (int64, error) {
	input = strings.TrimSpace(input)

	if input == "" {
		return 0, &DateTimeConversionError{
			Input:  input,
			Reason: "input cannot be empty",
		}
	}

	// Check if it's a full datetime
	if dtc.dateTimeRegex.MatchString(input) {
		return dtc.convertDateTime(input)
	}

	// Check if it's date only
	if dtc.dateOnlyRegex.MatchString(input) {
		return dtc.convertDateOnly(input)
	}

	// Check if it's time only
	if dtc.timeOnlyRegex.MatchString(input) {
		return dtc.convertTimeOnly(input)
	}

	return 0, &DateTimeConversionError{
		Input:  input,
		Reason: "input does not match expected ISO 8601 format (YYYY-MM-DD, HH:mm:ss.sssZ, or YYYY-MM-DDTHH:mm:ss.sssZ)",
	}
}

// convertDateTime converts a full datetime string to YYYYMMDDHHMMSS format
// The time is converted to UTC before formatting
func (dtc *DateTimeConverter) convertDateTime(input string) (int64, error) {
	parsedTime, err := iso8601.ParseString(input)
	if err != nil {
		return 0, &DateTimeConversionError{
			Input:  input,
			Reason: "failed to parse datetime",
			Err:    err,
		}
	}

	// Convert to UTC for consistent formatting
	utcTime := parsedTime.UTC()

	// Format as YYYYMMDDHHMMSS
	formatted := utcTime.Format("20060102150405")
	result, err := strconv.ParseInt(formatted, 10, 64)
	if err != nil {
		return 0, &DateTimeConversionError{
			Input:  input,
			Reason: "failed to convert formatted datetime to integer",
			Err:    err,
		}
	}

	return result, nil
}

// convertDateOnly converts a date-only string to YYYYMMDD format
func (dtc *DateTimeConverter) convertDateOnly(input string) (int64, error) {
	// Parse as date (add time component for parsing)
	parsedTime, err := time.Parse("2006-01-02", input)
	if err != nil {
		return 0, &DateTimeConversionError{
			Input:  input,
			Reason: "failed to parse date",
			Err:    err,
		}
	}

	// Format as YYYYMMDD
	formatted := parsedTime.Format("20060102")
	result, err := strconv.ParseInt(formatted, 10, 64)
	if err != nil {
		return 0, &DateTimeConversionError{
			Input:  input,
			Reason: "failed to convert formatted date to integer",
			Err:    err,
		}
	}

	return result, nil
}

// convertTimeOnly converts a time-only string to HHMMSS format
// The time is converted to UTC before formatting
func (dtc *DateTimeConverter) convertTimeOnly(input string) (int64, error) {
	// For time-only parsing, we need to add a date component
	// Use a fixed date (1970-01-01) and parse as full datetime
	dateTimeInput := "1970-01-01T" + input

	parsedTime, err := iso8601.ParseString(dateTimeInput)
	if err != nil {
		return 0, &DateTimeConversionError{
			Input:  input,
			Reason: "failed to parse time",
			Err:    err,
		}
	}

	// Convert to UTC for consistent formatting
	utcTime := parsedTime.UTC()

	// Format as HHMMSS (ignore milliseconds)
	formatted := utcTime.Format("150405")
	result, err := strconv.ParseInt(formatted, 10, 64)
	if err != nil {
		return 0, &DateTimeConversionError{
			Input:  input,
			Reason: "failed to convert formatted time to integer",
			Err:    err,
		}
	}

	return result, nil
}

// ConvertISO8601ToFloat64 converts an ISO 8601 formatted string to the appropriate numeric format as float64
//
// Parameters:
// - input: ISO 8601 formatted string (date, time, or datetime)
//
// Returns:
// - float64: The converted numeric value
// - error: Any error encountered during conversion
//
// Conversion rules:
// - Date only (YYYY-MM-DD): returns YYYYMMDD (8-digit number)
// - Time only (HH:mm:ss.sssZ): returns HHMMSS (6-digit number, ignoring milliseconds)
// - DateTime (YYYY-MM-DDTHH:mm:ss.sssZ): returns YYYYMMDDHHMMSS (14-digit number)
func (dtc *DateTimeConverter) ConvertISO8601ToFloat64(input string) (float64, error) {
	// Reuse the int64 implementation and convert the result to float64
	intResult, err := dtc.ConvertISO8601Toint64(input)
	if err != nil {
		return 0, err
	}

	return float64(intResult), nil
}

// DateTimeComponents represents the individual integer components of a datetime
type DateTimeComponents struct {
	Year   int
	Month  int
	Day    int
	Hour   int
	Minute int
	Second int
}

// ConvertISO8601ToComponents converts an ISO 8601 formatted string to individual integer components
//
// Parameters:
// - input: ISO 8601 formatted string (date, time, or datetime)
//
// Returns:
// - DateTimeComponents: Struct containing individual integer components
// - error: Any error encountered during conversion
//
// For time-only inputs, date components will be set to 1970-01-01
// For date-only inputs, time components will be set to 00:00:00
func (dtc *DateTimeConverter) ConvertISO8601ToComponents(input string) (DateTimeComponents, error) {
	input = strings.TrimSpace(input)

	if input == "" {
		return DateTimeComponents{}, &DateTimeConversionError{
			Input:  input,
			Reason: "input cannot be empty",
		}
	}

	var parsedTime time.Time
	var err error

	// Check if it's a full datetime
	if dtc.dateTimeRegex.MatchString(input) {
		parsedTime, err = iso8601.ParseString(input)
		if err != nil {
			return DateTimeComponents{}, &DateTimeConversionError{
				Input:  input,
				Reason: "failed to parse datetime",
				Err:    err,
			}
		}
	} else if dtc.dateOnlyRegex.MatchString(input) {
		// Parse as date only
		parsedTime, err = time.Parse("2006-01-02", input)
		if err != nil {
			return DateTimeComponents{}, &DateTimeConversionError{
				Input:  input,
				Reason: "failed to parse date",
				Err:    err,
			}
		}
	} else if dtc.timeOnlyRegex.MatchString(input) {
		// For time-only parsing, add a fixed date component
		dateTimeInput := "1970-01-01T" + input
		parsedTime, err = iso8601.ParseString(dateTimeInput)
		if err != nil {
			return DateTimeComponents{}, &DateTimeConversionError{
				Input:  input,
				Reason: "failed to parse time",
				Err:    err,
			}
		}
	} else {
		return DateTimeComponents{}, &DateTimeConversionError{
			Input:  input,
			Reason: "input does not match expected ISO 8601 format (YYYY-MM-DD, HH:mm:ss.sssZ, or YYYY-MM-DDTHH:mm:ss.sssZ)",
		}
	}

	// Convert to UTC for consistent formatting
	utcTime := parsedTime.UTC()

	return DateTimeComponents{
		Year:   utcTime.Year(),
		Month:  int(utcTime.Month()),
		Day:    utcTime.Day(),
		Hour:   utcTime.Hour(),
		Minute: utcTime.Minute(),
		Second: utcTime.Second(),
	}, nil
}

// ConvertISO8601ToSeparateIntegers converts an ISO 8601 formatted string to separate integer values
//
// Parameters:
// - input: ISO 8601 formatted string (date, time, or datetime)
//
// Returns:
// - year, month, day, hour, minute, second: Individual integer components
// - error: Any error encountered during conversion
//
// For time-only inputs, date components will be set to 1970-01-01
// For date-only inputs, time components will be set to 00:00:00
func (dtc *DateTimeConverter) ConvertISO8601ToSeparateIntegers(input string) (year, month, day, hour, minute, second int, err error) {
	components, err := dtc.ConvertISO8601ToComponents(input)
	if err != nil {
		return 0, 0, 0, 0, 0, 0, err
	}

	return components.Year, components.Month, components.Day, components.Hour, components.Minute, components.Second, nil
}

// ExtractYear extracts only the year component as an integer
func (dtc *DateTimeConverter) ExtractYear(input string) (int, error) {
	components, err := dtc.ConvertISO8601ToComponents(input)
	if err != nil {
		return 0, err
	}
	return components.Year, nil
}

// ExtractMonth extracts only the month component as an integer (1-12)
func (dtc *DateTimeConverter) ExtractMonth(input string) (int, error) {
	components, err := dtc.ConvertISO8601ToComponents(input)
	if err != nil {
		return 0, err
	}
	return components.Month, nil
}

// ExtractDay extracts only the day component as an integer (1-31)
func (dtc *DateTimeConverter) ExtractDay(input string) (int, error) {
	components, err := dtc.ConvertISO8601ToComponents(input)
	if err != nil {
		return 0, err
	}
	return components.Day, nil
}

// ExtractHour extracts only the hour component as an integer (0-23)
func (dtc *DateTimeConverter) ExtractHour(input string) (int, error) {
	components, err := dtc.ConvertISO8601ToComponents(input)
	if err != nil {
		return 0, err
	}
	return components.Hour, nil
}

// ExtractMinute extracts only the minute component as an integer (0-59)
func (dtc *DateTimeConverter) ExtractMinute(input string) (int, error) {
	components, err := dtc.ConvertISO8601ToComponents(input)
	if err != nil {
		return 0, err
	}
	return components.Minute, nil
}

// ExtractSecond extracts only the second component as an integer (0-59)
func (dtc *DateTimeConverter) ExtractSecond(input string) (int, error) {
	components, err := dtc.ConvertISO8601ToComponents(input)
	if err != nil {
		return 0, err
	}
	return components.Second, nil
}
