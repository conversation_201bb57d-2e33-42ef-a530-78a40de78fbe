# DateTime Converter Utility

A Go utility for converting ISO 8601 formatted date/time strings into numeric formats suitable for data processing and machine learning applications.

## Overview

This utility focuses on converting ISO 8601 formatted strings in the format `YYYY-MM-DDTHH:mm:ss.sssZ` (where T separates date and time, and Z indicates UTC or timezone offset) into compact numeric representations.

## Features

- **Date-only conversion**: `YYYY-MM-DD` → `YYYYMMDD` (8-digit number)
- **Time-only conversion**: `HH:mm:ss.sssZ` → `HHMMSS` (6-digit number, milliseconds ignored)
- **DateTime conversion**: `YYYY-MM-DDTHH:mm:ss.sssZ` → `YYYYMMDDHHMMSS` (14-digit number)
- **Multiple output formats**: Support for both int64 and float64 numeric representations
- **Component extraction**: Extract individual date/time components as separate integers
- **UTC normalization**: All times are converted to UTC before formatting
- **Timezone support**: Handles timezone offsets (e.g., `+05:30`, `-08:00`)
- **Comprehensive validation**: Input format validation with detailed error messages
- **High performance**: Optimized for processing large datasets

## Dependencies

This utility uses the [github.com/relvacode/iso8601](https://github.com/relvacode/iso8601) library for robust ISO 8601 parsing.

## Usage

### Basic Usage

```go
package main

import (
    "fmt"
    "log"

	datetimeconverter "github.com/berrijam/mulberri/internals/utils/datetime_converter"
)

func main() {
    // Create a new converter instance
    converter := datetimeconverter.NewDateTimeConverter()

    // Convert different types of inputs

    // Date only (int64)
    result, err := converter.ConvertISO8601Toint64("2023-12-25")
    if err != nil {
        log.Fatal(err)
    }
    fmt.Println(result) // Output: 20231225

    // Time only (float64)
    floatResult, err := converter.ConvertISO8601ToFloat64("14:30:45Z")
    if err != nil {
        log.Fatal(err)
    }
    fmt.Println(floatResult) // Output: 143045

    // DateTime (int64)
    result, err = converter.ConvertISO8601Toint64("2023-12-25T14:30:45.123Z")
    if err != nil {
        log.Fatal(err)
    }
    fmt.Println(result) // Output: 20231225143045

    // Extract components
    components, err := converter.ConvertISO8601ToComponents("2023-12-25T14:30:45Z")
    if err != nil {
        log.Fatal(err)
    }
    fmt.Printf("Year: %d, Month: %d, Day: %d\n", components.Year, components.Month, components.Day)
    // Output: Year: 2023, Month: 12, Day: 25

    // Extract as separate integers
    year, month, day, hour, minute, second, err := converter.ConvertISO8601ToSeparateIntegers("2023-12-25T14:30:45Z")
    if err != nil {
        log.Fatal(err)
    }
    fmt.Printf("%d-%02d-%02d %02d:%02d:%02d\n", year, month, day, hour, minute, second)
    // Output: 2023-12-25 14:30:45

    // Extract individual components
    year, err = converter.ExtractYear("2023-12-25T14:30:45Z")
    if err != nil {
        log.Fatal(err)
    }
    fmt.Println("Year:", year) // Output: Year: 2023
}
```
## Available Methods

### Numeric Conversion Methods

#### `ConvertISO8601Toint64(input string) (int64, error)`
Converts ISO 8601 string to int64 format. Returns the datetime as a 14-digit integer (YYYYMMDDHHMMSS).

#### `ConvertISO8601ToFloat64(input string) (float64, error)`
Converts ISO 8601 string to float64 format. Same numeric value as int64 but as floating-point.

### Component Extraction Methods

#### `ConvertISO8601ToComponents(input string) (DateTimeComponents, error)`
Extracts all datetime components into a structured format:
```go
type DateTimeComponents struct {
    Year   int
    Month  int
    Day    int
    Hour   int
    Minute int
    Second int
}
```

#### `ConvertISO8601ToSeparateIntegers(input string) (year, month, day, hour, minute, second int, err error)`
Returns all datetime components as separate integer values in a single function call.

### Individual Component Extraction Methods

- `ExtractYear(input string) (int, error)` - Extract only the year
- `ExtractMonth(input string) (int, error)` - Extract only the month (1-12)
- `ExtractDay(input string) (int, error)` - Extract only the day (1-31)
- `ExtractHour(input string) (int, error)` - Extract only the hour (0-23)
- `ExtractMinute(input string) (int, error)` - Extract only the minute (0-59)
- `ExtractSecond(input string) (int, error)` - Extract only the second (0-59)





## Supported Input Formats

### Date Only
- `2023-12-25`

### Time Only
- `14:30:45Z` (UTC)
- `14:30:45.123Z` (with milliseconds)
- `14:30:45+05:30` (with timezone offset)
- `14:30:45-08:00` (with negative timezone offset)

### DateTime
- `2023-12-25T14:30:45Z`
- `2023-12-25T14:30:45.123Z`
- `2023-12-25T14:30:45+05:30`
- `2023-12-25T14:30:45.123-08:00`

## Conversion Examples

| Input | Type | Output |
|-------|------|--------|
| `2023-12-25` | Date | `20231225` |
| `14:30:45Z` | Time | `143045` |
| `09:15:30+05:30` | Time | `34530` |
| `2023-12-25T14:30:45Z` | DateTime | `20231225143045` ||
| `2023-06-15T09:30:45+05:30` | DateTime | `20230615040045` |



## Error Handling

The utility provides detailed error messages for various failure scenarios:

```go
converter := datetimeconverter.NewDateTimeConverter()

// Empty input
_, err := converter.ConvertISO8601("")
// Error: datetime conversion failed for input '': input cannot be empty

// Invalid format
_, err = converter.ConvertISO8601("2023/12/25")
// Error: datetime conversion failed for input '2023/12/25': input does not match expected ISO 8601 format

// Time without timezone
_, err = converter.ConvertISO8601("14:30:45")
// Error: datetime conversion failed for input '14:30:45': input does not match expected ISO 8601 format
```

## Use Cases

This utility is particularly useful for:

- **Data preprocessing**: Converting date/time strings in datasets before ML training
- **Feature engineering**: Creating numeric date/time features for machine learning models
- **Data storage optimization**: Storing date/time values as compact integers
- **ETL pipelines**: Transforming date/time data during data processing workflows
- **Time series analysis**: Converting timestamps to numeric format for analysis

## Testing

The utility includes comprehensive tests covering:
- All supported input formats
- Timezone conversion accuracy
- Error handling scenarios
- Edge cases (leap years, midnight, end of day)
- Performance benchmarks

