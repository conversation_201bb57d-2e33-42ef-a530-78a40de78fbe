package datetimeconverter

import (
	"fmt"
	"log"
)

// ExampleUsage demonstrates the datetime converter functionality
func ExampleUsage() {
	// Create a new converter instance
	converter := NewDateTimeConverter()

	// Example datetime inputs
	examples := []string{
		"2023-12-25T14:30:45.123Z",
		"2023-12-25",
		"14:30:45Z",
		"2023-06-15T09:30:45+05:30",
	}

	fmt.Println("=== DateTime Converter Examples ===")

	for _, input := range examples {
		fmt.Printf("Input: %s\n", input)

		// 1. Convert to int64
		int64Result, err := converter.ConvertISO8601Toint64(input)
		if err != nil {
			log.Printf("Error converting to int64: %v", err)
			continue
		}
		fmt.Printf("  Int64:    %d\n", int64Result)

		// 2. Convert to float64
		float64Result, err := converter.ConvertISO8601ToFloat64(input)
		if err != nil {
			log.Printf("Error converting to float64: %v", err)
			continue
		}
		fmt.Printf("  Float64:  %.0f\n", float64Result)

		// 3. Extract components
		components, err := converter.ConvertISO8601ToComponents(input)
		if err != nil {
			log.Printf("Error extracting components: %v", err)
			continue
		}
		fmt.Printf("  Components: Year=%d, Month=%d, Day=%d, Hour=%d, Minute=%d, Second=%d\n",
			components.Year, components.Month, components.Day,
			components.Hour, components.Minute, components.Second)

		// 4. Extract as separate integers
		year, month, day, hour, minute, second, err := converter.ConvertISO8601ToSeparateIntegers(input)
		if err != nil {
			log.Printf("Error extracting separate integers: %v", err)
			continue
		}
		fmt.Printf("  Separate: %d-%02d-%02d %02d:%02d:%02d\n",
			year, month, day, hour, minute, second)

		// 5. Extract individual components
		extractedYear, _ := converter.ExtractYear(input)
		extractedMonth, _ := converter.ExtractMonth(input)
		extractedDay, _ := converter.ExtractDay(input)
		extractedHour, _ := converter.ExtractHour(input)
		extractedMinute, _ := converter.ExtractMinute(input)
		extractedSecond, _ := converter.ExtractSecond(input)

		fmt.Printf("  Individual: Y=%d, M=%d, D=%d, H=%d, Min=%d, S=%d\n",
			extractedYear, extractedMonth, extractedDay,
			extractedHour, extractedMinute, extractedSecond)

		fmt.Println()
	}

	// Demonstrate error handling
	fmt.Println("=== Error Handling Examples ===")
	invalidInputs := []string{
		"",
		"invalid-datetime",
		"2023/12/25",
		"14:30:45", // missing timezone
	}

	for _, input := range invalidInputs {
		fmt.Printf("Invalid Input: '%s'\n", input)
		_, err := converter.ConvertISO8601Toint64(input)
		if err != nil {
			fmt.Printf("  Error: %v\n", err)
		}
		fmt.Println()
	}

	// Demonstrate timezone handling
	fmt.Println("=== Timezone Conversion Examples ===")
	timezoneExamples := []string{
		"14:30:45Z",      // UTC
		"14:30:45+05:30", // India Standard Time
		"14:30:45-08:00", // Pacific Standard Time
		"14:30:45+00:00", // UTC (explicit)
	}

	for _, input := range timezoneExamples {
		fmt.Printf("Input: %s\n", input)
		components, err := converter.ConvertISO8601ToComponents(input)
		if err != nil {
			log.Printf("Error: %v", err)
			continue
		}
		fmt.Printf("  UTC Time: %02d:%02d:%02d\n", components.Hour, components.Minute, components.Second)
		fmt.Println()
	}
}
