package datetimeconverter

import (
	"fmt"
	"testing"
)

// TestDemoNewFunctionality demonstrates the new datetime splitting functionality
func TestDemoNewFunctionality(t *testing.T) {
	converter := NewDateTimeConverter()

	// Example datetime inputs
	examples := []string{
		"2023-12-25T14:30:45.123Z",
		"2023-12-25",
		"14:30:45Z",
		"2023-06-15T09:30:45+05:30",
	}

	fmt.Println("\n=== DateTime Converter Demo ===")

	for _, input := range examples {
		fmt.Printf("\nInput: %s\n", input)

		// 1. Convert to int64
		int64Result, err := converter.ConvertISO8601Toint64(input)
		if err != nil {
			fmt.Printf("  Error converting to int64: %v\n", err)
			continue
		}
		fmt.Printf("  Int64:    %d\n", int64Result)

		// 2. Convert to float64
		float64Result, err := converter.ConvertISO8601ToFloat64(input)
		if err != nil {
			fmt.Printf("  Error converting to float64: %v\n", err)
			continue
		}
		fmt.Printf("  Float64:  %.0f\n", float64Result)

		// 3. Extract components
		components, err := converter.ConvertISO8601ToComponents(input)
		if err != nil {
			fmt.Printf("  Error extracting components: %v\n", err)
			continue
		}
		fmt.Printf("  Components: Year=%d, Month=%d, Day=%d, Hour=%d, Minute=%d, Second=%d\n",
			components.Year, components.Month, components.Day,
			components.Hour, components.Minute, components.Second)

		// 4. Extract as separate integers
		year, month, day, hour, minute, second, err := converter.ConvertISO8601ToSeparateIntegers(input)
		if err != nil {
			fmt.Printf("  Error extracting separate integers: %v\n", err)
			continue
		}
		fmt.Printf("  Separate: %d-%02d-%02d %02d:%02d:%02d\n",
			year, month, day, hour, minute, second)

		// 5. Extract individual components
		extractedYear, _ := converter.ExtractYear(input)
		extractedMonth, _ := converter.ExtractMonth(input)
		extractedDay, _ := converter.ExtractDay(input)
		extractedHour, _ := converter.ExtractHour(input)
		extractedMinute, _ := converter.ExtractMinute(input)
		extractedSecond, _ := converter.ExtractSecond(input)

		fmt.Printf("  Individual: Y=%d, M=%d, D=%d, H=%d, Min=%d, S=%d\n",
			extractedYear, extractedMonth, extractedDay,
			extractedHour, extractedMinute, extractedSecond)
	}

	// Demonstrate timezone handling
	fmt.Println("\n=== Timezone Conversion Examples ===")
	timezoneExamples := []string{
		"14:30:45Z",           // UTC
		"14:30:45+05:30",      // India Standard Time
		"14:30:45-08:00",      // Pacific Standard Time
		"14:30:45+00:00",      // UTC (explicit)
	}

	for _, input := range timezoneExamples {
		fmt.Printf("\nInput: %s\n", input)
		components, err := converter.ConvertISO8601ToComponents(input)
		if err != nil {
			fmt.Printf("  Error: %v\n", err)
			continue
		}
		fmt.Printf("  UTC Time: %02d:%02d:%02d\n", components.Hour, components.Minute, components.Second)
	}

	fmt.Println("\n=== Demo Complete ===")
}

// TestCompareDataTypes demonstrates the difference between int64 and float64 outputs
func TestCompareDataTypes(t *testing.T) {
	converter := NewDateTimeConverter()
	input := "2023-12-25T14:30:45Z"

	fmt.Printf("\n=== Data Type Comparison for: %s ===\n", input)

	// Get int64 result
	int64Result, err := converter.ConvertISO8601Toint64(input)
	if err != nil {
		t.Fatalf("Error getting int64: %v", err)
	}

	// Get float64 result
	float64Result, err := converter.ConvertISO8601ToFloat64(input)
	if err != nil {
		t.Fatalf("Error getting float64: %v", err)
	}

	fmt.Printf("Int64 result:   %d\n", int64Result)
	fmt.Printf("Float64 result: %.0f\n", float64Result)
	fmt.Printf("Are they equal? %t\n", int64Result == int64(float64Result))
	fmt.Printf("Float64 as int64: %d\n", int64(float64Result))
}

// TestComponentExtractionPerformance demonstrates individual component extraction
func TestComponentExtractionPerformance(t *testing.T) {
	converter := NewDateTimeConverter()
	input := "2023-12-25T14:30:45Z"

	fmt.Printf("\n=== Component Extraction Methods for: %s ===\n", input)

	// Method 1: Extract all components at once
	components, err := converter.ConvertISO8601ToComponents(input)
	if err != nil {
		t.Fatalf("Error getting components: %v", err)
	}
	fmt.Printf("All at once: %+v\n", components)

	// Method 2: Extract as separate integers
	year, month, day, hour, minute, second, err := converter.ConvertISO8601ToSeparateIntegers(input)
	if err != nil {
		t.Fatalf("Error getting separate integers: %v", err)
	}
	fmt.Printf("Separate integers: Y=%d, M=%d, D=%d, H=%d, Min=%d, S=%d\n",
		year, month, day, hour, minute, second)

	// Method 3: Extract individual components (less efficient for multiple components)
	individualYear, _ := converter.ExtractYear(input)
	individualMonth, _ := converter.ExtractMonth(input)
	fmt.Printf("Individual extraction: Year=%d, Month=%d\n", individualYear, individualMonth)
}
