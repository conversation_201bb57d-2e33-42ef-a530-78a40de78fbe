package utils

import (
	"reflect"
	"testing"

	"github.com/berrijam/mulberri/pkg/models"
)

func TestCSVDataset(t *testing.T) {
	features := [][]string{
		{"5.1", "setosa", "red"},
		{"4.9", "versicolor", "blue"},
		{"6.2", "virginica", "red"},
	}
	targets := []string{"iris1", "iris2", "iris3"}

	dataset := NewCSVDataset(features, targets)

	// Test basic properties
	if dataset.GetSize() != 3 {
		t.<PERSON><PERSON><PERSON>("Expected size 3, got %d", dataset.GetSize())
	}

	expectedIndices := []int{0, 1, 2}
	if !reflect.DeepEqual(dataset.GetIndices(), expectedIndices) {
		t.<PERSON>rf("Expected indices %v, got %v", expectedIndices, dataset.GetIndices())
	}

	// Test target retrieval
	target, err := dataset.GetTarget(1)
	if err != nil {
		t.<PERSON>rf("Unexpected error getting target: %v", err)
	}
	if target != "iris2" {
		t.<PERSON><PERSON><PERSON>("Expected target 'iris2', got '%s'", target)
	}

	// Test feature value retrieval
	numericFeature := &models.Feature{
		Name:         "sepal_length",
		Type:         models.NumericFeature,
		ColumnNumber: 0,
	}

	value, err := dataset.GetFeatureValue(0, numericFeature)
	if err != nil {
		t.Errorf("Unexpected error getting feature value: %v", err)
	}

	// Should be converted to float64
	if floatVal, ok := value.(float64); !ok || floatVal != 5.1 {
		t.Errorf("Expected numeric value 5.1, got %v (type %T)", value, value)
	}

	// Test categorical feature
	categoricalFeature := &models.Feature{
		Name:         "species",
		Type:         models.CategoricalFeature,
		ColumnNumber: 1,
	}

	value, err = dataset.GetFeatureValue(1, categoricalFeature)
	if err != nil {
		t.Errorf("Unexpected error getting categorical feature: %v", err)
	}

	if strVal, ok := value.(string); !ok || strVal != "versicolor" {
		t.Errorf("Expected categorical value 'versicolor', got %v (type %T)", value, value)
	}
}

func TestCSVDatasetSubset(t *testing.T) {
	features := [][]string{
		{"1.0", "a"},
		{"2.0", "b"},
		{"3.0", "c"},
		{"4.0", "d"},
	}
	targets := []string{"x", "y", "z", "w"}

	dataset := NewCSVDataset(features, targets)

	// Create subset with indices [1, 3]
	subset := dataset.Subset([]int{1, 3})

	if subset.GetSize() != 2 {
		t.Errorf("Expected subset size 2, got %d", subset.GetSize())
	}

	expectedIndices := []int{1, 3}
	if !reflect.DeepEqual(subset.GetIndices(), expectedIndices) {
		t.Errorf("Expected subset indices %v, got %v", expectedIndices, subset.GetIndices())
	}

	// Test that subset still references original data
	target, err := subset.GetTarget(1)
	if err != nil {
		t.Errorf("Unexpected error: %v", err)
	}
	if target != "y" {
		t.Errorf("Expected target 'y', got '%s'", target)
	}
}

func TestCSVDatasetErrors(t *testing.T) {
	features := [][]string{
		{"1.0", "a"},
		{"2.0", "b"},
	}
	targets := []string{"x", "y"}

	dataset := NewCSVDataset(features, targets)

	// Test out of bounds sample index for target
	_, err := dataset.GetTarget(5)
	if err == nil {
		t.Error("Expected error for out of bounds target index")
	}

	// Test negative sample index for target
	_, err = dataset.GetTarget(-1)
	if err == nil {
		t.Error("Expected error for negative target index")
	}

	// Test out of bounds sample index for feature
	feature := &models.Feature{
		Name:         "test",
		Type:         models.NumericFeature,
		ColumnNumber: 0,
	}

	_, err = dataset.GetFeatureValue(5, feature)
	if err == nil {
		t.Error("Expected error for out of bounds feature sample index")
	}

	// Test out of bounds column number
	badFeature := &models.Feature{
		Name:         "test",
		Type:         models.NumericFeature,
		ColumnNumber: 10,
	}

	_, err = dataset.GetFeatureValue(0, badFeature)
	if err == nil {
		t.Error("Expected error for out of bounds feature column")
	}
}

func TestCSVDatasetPanic(t *testing.T) {
	// Since logger.Fatal() calls os.Exit(1), we can't test this directly in a unit test
	// without the process exiting. Instead, we test the validation logic indirectly
	// by ensuring that valid inputs work and documenting the expected behavior.

	// Test that valid inputs work (no exit)
	features := [][]string{
		{"1.0", "a"},
		{"2.0", "b"},
	}
	targets := []string{"x", "y"} // Matching length

	dataset := NewCSVDataset(features, targets)
	if dataset.GetSize() != 2 {
		t.Errorf("Expected size 2, got %d", dataset.GetSize())
	}

	// Note: Testing mismatched lengths would cause os.Exit(1) via logger.Fatal()
	// This is the expected behavior for this critical error condition.
}

func TestCSVDatasetNumericConversionFailure(t *testing.T) {
	features := [][]string{
		{"not_a_number", "a"},
	}
	targets := []string{"x"}

	dataset := NewCSVDataset(features, targets)

	numericFeature := &models.Feature{
		Name:         "test",
		Type:         models.NumericFeature,
		ColumnNumber: 0,
	}

	// Should return the original string value when conversion fails
	value, err := dataset.GetFeatureValue(0, numericFeature)
	if err != nil {
		t.Errorf("Unexpected error: %v", err)
	}

	if strVal, ok := value.(string); !ok || strVal != "not_a_number" {
		t.Errorf("Expected string value 'not_a_number', got %v (type %T)", value, value)
	}
}
