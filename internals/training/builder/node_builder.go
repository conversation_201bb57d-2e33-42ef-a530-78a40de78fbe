package builder

import (
	"fmt"

	"github.com/berrijam/mulberri/internals/training"
	"github.com/berrijam/mulberri/pkg/models"
)

// DefaultNodeBuilder provides the default implementation of NodeBuilder interface
type DefaultNodeBuilder[T comparable] struct {
	config TreeConfiguration
}

// NewDefaultNodeBuilder creates a new default node builder
func NewDefaultNodeBuilder[T comparable](config TreeConfiguration) *DefaultNodeBuilder[T] {
	return &DefaultNodeBuilder[T]{
		config: config,
	}
}

// BuildLeafNode creates a leaf node from node statistics
func (nb *DefaultNodeBuilder[T]) BuildLeafNode(stats *NodeStatistics[T]) (*models.TreeNode, error) {
	if stats == nil {
		return nil, &BuilderError{
			Op:     "build_leaf_node",
			Field:  "statistics",
			Reason: "node statistics cannot be nil",
		}
	}

	// Convert class distribution using type-safe helper
	interfaceDistribution := nb.convertClassDistribution(stats.ClassDistribution)
	prediction := nb.convertPrediction(stats.MajorityClass)

	leaf, err := models.NewLeafNode(prediction, interfaceDistribution, stats.SampleCount)
	if err != nil {
		return nil, &BuilderError{
			Op:     "build_leaf_node",
			Reason: fmt.Sprintf("failed to create leaf node: %v", err),
			Err:    err,
		}
	}

	// Set additional statistics
	leaf.Confidence = stats.Confidence
	leaf.Impurity = stats.Impurity

	return leaf, nil
}

// BuildDecisionNode creates a decision node from split result and children
func (nb *DefaultNodeBuilder[T]) BuildDecisionNode(split *training.SplitResult[T], leftChild, rightChild *models.TreeNode) (*models.TreeNode, error) {
	if split == nil {
		return nil, &BuilderError{
			Op:     "build_decision_node",
			Field:  "split",
			Reason: "split result cannot be nil",
		}
	}

	if split.Feature == nil {
		return nil, &BuilderError{
			Op:     "build_decision_node",
			Field:  "feature",
			Reason: "split feature cannot be nil",
		}
	}

	var node *models.TreeNode
	var err error

	// Create appropriate decision node based on feature type
	switch split.Feature.Type {
	case models.NumericFeature, models.DateFeature:
		node, err = models.NewDecisionNode(split.Feature, split.Threshold)
		if err != nil {
			return nil, &BuilderError{
				Op:     "build_decision_node",
				Reason: fmt.Sprintf("failed to create numeric decision node: %v", err),
				Err:    err,
			}
		}

		// Set children for numeric split
		if leftChild != nil {
			if err := node.SetLeftChild(leftChild); err != nil {
				return nil, &BuilderError{
					Op:     "build_decision_node",
					Field:  "left_child",
					Reason: fmt.Sprintf("failed to set left child: %v", err),
					Err:    err,
				}
			}
		}

		if rightChild != nil {
			if err := node.SetRightChild(rightChild); err != nil {
				return nil, &BuilderError{
					Op:     "build_decision_node",
					Field:  "right_child",
					Reason: fmt.Sprintf("failed to set right child: %v", err),
					Err:    err,
				}
			}
		}

	case models.CategoricalFeature:
		node, err = models.NewCategoricalDecisionNode(split.Feature)
		if err != nil {
			return nil, &BuilderError{
				Op:     "build_decision_node",
				Reason: fmt.Sprintf("failed to create categorical decision node: %v", err),
				Err:    err,
			}
		}

		// For categorical splits, we would need to handle multiple children
		// This is a simplified implementation for binary splits
		if leftChild != nil {
			if err := node.SetChild("left", leftChild); err != nil {
				return nil, &BuilderError{
					Op:     "build_decision_node",
					Field:  "categorical_child",
					Reason: fmt.Sprintf("failed to set categorical child: %v", err),
					Err:    err,
				}
			}
		}

	default:
		return nil, &BuilderError{
			Op:     "build_decision_node",
			Field:  "feature_type",
			Value:  string(split.Feature.Type),
			Reason: "unsupported feature type for decision node",
		}
	}

	return node, nil
}

// convertClassDistribution safely converts generic class distribution to interface{} map
func (nb *DefaultNodeBuilder[T]) convertClassDistribution(distribution map[T]int) map[interface{}]int {
	if distribution == nil {
		return make(map[interface{}]int)
	}
	
	result := make(map[interface{}]int, len(distribution))
	for class, count := range distribution {
		result[interface{}(class)] = count
	}
	return result
}

// convertPrediction safely converts generic prediction to interface{}
func (nb *DefaultNodeBuilder[T]) convertPrediction(prediction T) interface{} {
	return interface{}(prediction)
}
