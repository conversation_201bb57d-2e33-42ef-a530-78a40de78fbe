// Package prediction contains tree traversal and prediction logic.
package prediction

import (
	"fmt"
	"math"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/berrijam/mulberri/pkg/models"
)

// TraversalResult contains the result of tree traversal
type TraversalResult struct {
	Prediction        interface{}             `json:"prediction"`         // Final prediction value
	ClassDistribution map[interface{}]int     `json:"class_distribution"` // Class distribution at leaf
	Confidence        float64                 `json:"confidence"`         // Confidence score (0-1)
	Path              []string                `json:"path"`               // Path taken through tree
	RulePath          string                  `json:"rule"`               // Decision rule in conjunction format
	LeafNode          *models.TreeNode        `json:"-"`                  // Reference to final leaf node
	Probabilities     map[interface{}]float64 `json:"probabilities"`      // Normalized probabilities
}

// TraversalMetrics contains performance metrics for traversal
type TraversalMetrics struct {
	NodesVisited    int           `json:"nodes_visited"`
	MaxDepthReached int           `json:"max_depth_reached"`
	TraversalTime   time.Duration `json:"traversal_time"`
	Strategy        string        `json:"strategy"` // "recursive" or "iterative"
}

// TraversalStrategy defines the traversal approach
type TraversalStrategy string

const (
	RecursiveTraversal TraversalStrategy = "recursive"
	IterativeTraversal TraversalStrategy = "iterative"
	AutoStrategy       TraversalStrategy = "auto"
)

// MissingValueStrategy defines how to handle missing values during traversal
type MissingValueStrategy string

const (
	// UseMajorityClass sends missing values to the most common branch
	UseMajorityClass MissingValueStrategy = "majority_class"
	// UseDefaultPrediction returns the prediction from current node when value is missing
	UseDefaultPrediction MissingValueStrategy = "default_prediction"
	// FailOnMissing returns an error when a missing value is encountered
	FailOnMissing MissingValueStrategy = "fail_on_missing"
)

// TraversalOptions configures tree traversal behavior
type TraversalOptions struct {
	MissingValueStrategy MissingValueStrategy `json:"missing_value_strategy"`
	IncludePath          bool                 `json:"include_path"`
	MaxDepth             int                  `json:"max_depth"` // Prevent infinite loops
	Strategy             TraversalStrategy    `json:"strategy"`  // Traversal strategy
	CollectMetrics       bool                 `json:"collect_metrics"`
}

const (
	RECURSIVE_THRESHOLD = 50 // Switch to iterative for trees deeper than this
	DEFAULT_MAX_DEPTH   = 1000
)

// depthCache provides thread-safe caching of tree depths
var depthCache = struct {
	mu    sync.RWMutex
	cache map[*models.DecisionTree]int
}{
	cache: make(map[*models.DecisionTree]int),
}

// DefaultTraversalOptions returns sensible default options
func DefaultTraversalOptions() TraversalOptions {
	return TraversalOptions{
		MissingValueStrategy: UseMajorityClass,
		IncludePath:          false,
		MaxDepth:             DEFAULT_MAX_DEPTH,
		Strategy:             AutoStrategy,
		CollectMetrics:       false,
	}
}

// ValidateInputRecord validates the input record against tree features
func ValidateInputRecord(record map[string]interface{}, tree *models.DecisionTree) error {
	if record == nil {
		return &models.ModelError{
			Op:     "validate_input_record",
			Field:  "record",
			Reason: "record cannot be nil",
		}
	}

	for featureName, feature := range tree.Features {
		value, exists := record[featureName]
		if !exists {
			continue // Will be handled by missing value strategy
		}

		if err := validateFeatureValue(value, feature); err != nil {
			return &models.ModelError{
				Op:     "validate_input_record",
				Field:  featureName,
				Value:  fmt.Sprintf("%v", value),
				Reason: fmt.Sprintf("invalid feature value: %v", err),
				Err:    err,
			}
		}
	}

	return nil
}

// validateFeatureValue validates a single feature value
func validateFeatureValue(value interface{}, feature *models.Feature) error {
	if value == nil {
		return nil // Handled by missing value strategy
	}

	switch feature.Type {
	case models.NumericFeature, models.DateFeature:
		if _, err := convertToFloat64(value); err != nil {
			return fmt.Errorf("cannot convert to numeric: %v", err)
		}
	case models.CategoricalFeature:
		// Any value can be converted to string for categorical
		return nil
	default:
		return fmt.Errorf("unsupported feature type: %v", feature.Type)
	}

	return nil
}

// isSingleNodeTree checks if the tree has only one node
func isSingleNodeTree(tree *models.DecisionTree) bool {
	return tree.Root != nil && tree.Root.IsLeaf()
}

// getTreeDepth gets the cached depth or computes and caches it
func getTreeDepth(tree *models.DecisionTree) int {
	if tree == nil || tree.Root == nil {
		return 0
	}

	// Try to get from cache first (read lock)
	depthCache.mu.RLock()
	if depth, exists := depthCache.cache[tree]; exists {
		depthCache.mu.RUnlock()
		return depth
	}
	depthCache.mu.RUnlock()

	// Not in cache, compute it (write lock)
	depthCache.mu.Lock()
	defer depthCache.mu.Unlock()

	// Double-check in case another goroutine computed it while we waited
	if depth, exists := depthCache.cache[tree]; exists {
		return depth
	}

	// Compute and cache the depth
	depth := estimateTreeDepth(tree.Root)
	depthCache.cache[tree] = depth
	return depth
}

// ClearDepthCache clears the depth cache (useful for testing or memory cleanup)
func ClearDepthCache() {
	depthCache.mu.Lock()
	defer depthCache.mu.Unlock()
	depthCache.cache = make(map[*models.DecisionTree]int)
}

// estimateTreeDepth estimates the maximum depth of the tree (internal function)
func estimateTreeDepth(node *models.TreeNode) int {
	if node == nil || node.IsLeaf() {
		return 0
	}

	maxDepth := 0

	// Check left and right children
	if node.Left != nil {
		depth := 1 + estimateTreeDepth(node.Left)
		if depth > maxDepth {
			maxDepth = depth
		}
	}

	if node.Right != nil {
		depth := 1 + estimateTreeDepth(node.Right)
		if depth > maxDepth {
			maxDepth = depth
		}
	}

	// Check categorical children
	for _, child := range node.Categories {
		if child != nil {
			depth := 1 + estimateTreeDepth(child)
			if depth > maxDepth {
				maxDepth = depth
			}
		}
	}

	return maxDepth
}

// chooseTraversalStrategy automatically selects the best traversal strategy
func chooseTraversalStrategy(tree *models.DecisionTree, options TraversalOptions) TraversalStrategy {
	if options.Strategy != AutoStrategy {
		return options.Strategy
	}

	// For single node trees, strategy doesn't matter
	if isSingleNodeTree(tree) {
		return RecursiveTraversal
	}

	// Estimate tree depth using cached value
	estimatedDepth := getTreeDepth(tree)

	if estimatedDepth > RECURSIVE_THRESHOLD {
		return IterativeTraversal
	}

	return RecursiveTraversal
}

// normalizeClassDistribution converts class counts to probabilities
func normalizeClassDistribution(classDistribution map[interface{}]int) map[interface{}]float64 {
	probabilities := make(map[interface{}]float64)
	total := 0

	// Calculate total count
	for _, count := range classDistribution {
		total += count
	}

	if total == 0 {
		return probabilities
	}

	// Convert to probabilities
	for class, count := range classDistribution {
		probabilities[class] = float64(count) / float64(total)
	}

	return probabilities
}

// buildRule converts path steps to conjunction format
func buildRule(path []string) string {
	if len(path) == 0 {
		return ""
	}

	var conditions []string
	var leafPrediction string

	for _, step := range path {
		if strings.HasPrefix(step, "LEAF[") {
			leafPrediction = step
		} else if !strings.HasPrefix(step, "MISSING[") {
			conditions = append(conditions, step)
		}
	}

	if len(conditions) == 0 {
		return leafPrediction
	}

	rule := strings.Join(conditions, " & ")
	if leafPrediction != "" {
		rule += " --> " + leafPrediction
	}

	return "[" + rule + "]"
}

// TraverseTree traverses a decision tree to make a prediction for the given input record
func TraverseTree(tree *models.DecisionTree, record map[string]interface{}, options TraversalOptions) (*TraversalResult, error) {
	if tree == nil {
		return nil, &models.ModelError{
			Op:     "traverse_tree",
			Field:  "tree",
			Reason: "tree cannot be nil",
		}
	}

	if tree.Root == nil {
		return nil, &models.ModelError{
			Op:     "traverse_tree",
			Field:  "root",
			Reason: "tree has no root node (tree not trained)",
		}
	}

	if record == nil {
		return nil, &models.ModelError{
			Op:     "traverse_tree",
			Field:  "record",
			Reason: "input record cannot be nil",
		}
	}

	// Validate input record
	if err := ValidateInputRecord(record, tree); err != nil {
		return nil, err
	}

	// Choose traversal strategy
	strategy := chooseTraversalStrategy(tree, options)

	var startTime time.Time
	if options.CollectMetrics {
		startTime = time.Now()
	}

	// Initialize traversal state
	var path []string
	if options.IncludePath {
		path = make([]string, 0, getTreeDepth(tree))
	}

	var leafNode *models.TreeNode
	var traversalPath []string
	var metrics *TraversalMetrics
	var err error

	// Execute traversal based on strategy
	switch strategy {
	case IterativeTraversal:
		leafNode, traversalPath, metrics, err = traverseNodeIterative(tree.Root, record, tree.Features, path, options)
	default: // RecursiveTraversal
		if options.CollectMetrics {
			metrics = &TraversalMetrics{
				Strategy: string(RecursiveTraversal),
			}
			leafNode, traversalPath, metrics, err = traverseNodeRecursive(tree.Root, record, tree.Features, path, options, 0, metrics)
		} else {
			leafNode, traversalPath, _, err = traverseNodeRecursive(tree.Root, record, tree.Features, path, options, 0, nil)
		}
	}

	if err != nil {
		return nil, err
	}

	// Complete metrics collection
	if options.CollectMetrics && metrics != nil {
		metrics.TraversalTime = time.Since(startTime)
	}

	// Build result
	result := &TraversalResult{
		Prediction:        leafNode.Prediction,
		ClassDistribution: leafNode.ClassDistribution,
		Confidence:        leafNode.Confidence,
		LeafNode:          leafNode,
		Probabilities:     normalizeClassDistribution(leafNode.ClassDistribution),
	}

	if options.IncludePath {
		result.Path = traversalPath
		result.RulePath = buildRule(traversalPath)
	}

	return result, nil
}

// TraverseTreeWithMetrics traverses a tree and returns detailed performance metrics
func TraverseTreeWithMetrics(tree *models.DecisionTree, record map[string]interface{}, options TraversalOptions) (*TraversalResult, *TraversalMetrics, error) {
	if options.MaxDepth == 0 {
		options.MaxDepth = DEFAULT_MAX_DEPTH
	}
	options.CollectMetrics = true
	result, err := TraverseTree(tree, record, options)

	var metrics *TraversalMetrics
	if err == nil {
		// Metrics would be collected during traversal in a full implementation
		// For now, return basic metrics
		strategy := chooseTraversalStrategy(tree, options)
		metrics = &TraversalMetrics{
			Strategy:        string(strategy),
			NodesVisited:    len(result.Path), // Approximate
			MaxDepthReached: len(result.Path), // Approximate
		}
	}

	return result, metrics, err
}

// TraverseTreeSimple provides a simplified interface for tree traversal with default options
func TraverseTreeSimple(tree *models.DecisionTree, record map[string]interface{}) (*TraversalResult, error) {
	return TraverseTree(tree, record, DefaultTraversalOptions())
}

// traverseNodeIterative implements iterative tree traversal for deep trees
func traverseNodeIterative(root *models.TreeNode, record map[string]interface{}, features map[string]*models.Feature, path []string, options TraversalOptions) (*models.TreeNode, []string, *TraversalMetrics, error) {
	type stackItem struct {
		node  *models.TreeNode
		depth int
		path  []string
	}

	stack := []stackItem{{node: root, depth: 0, path: path}}
	metrics := &TraversalMetrics{
		Strategy: string(IterativeTraversal),
	}

	for len(stack) > 0 {
		// Pop from stack
		current := stack[len(stack)-1]
		stack = stack[:len(stack)-1]

		metrics.NodesVisited++
		if current.depth > metrics.MaxDepthReached {
			metrics.MaxDepthReached = current.depth
		}

		// Check depth limit
		if current.depth > options.MaxDepth {
			return nil, nil, metrics, &models.ModelError{
				Op:     "traverse_node_iterative",
				Field:  "depth",
				Value:  fmt.Sprintf("%d", current.depth),
				Reason: fmt.Sprintf("traversal depth exceeded limit of %d", options.MaxDepth),
			}
		}

		// Base case: reached a leaf node
		if current.node.IsLeaf() {
			if options.IncludePath {
				current.path = append(current.path, fmt.Sprintf("LEAF[%v]", current.node.Prediction))
			}
			return current.node, current.path, metrics, nil
		}

		// Decision node: determine which child to traverse
		if current.node.Feature == nil {
			return nil, nil, metrics, &models.ModelError{
				Op:     "traverse_node_iterative",
				Field:  "feature",
				Reason: "decision node has no feature",
			}
		}

		// Get the feature value from the input record
		featureValue, exists := record[current.node.Feature.Name]

		// Handle missing values
		if !exists || featureValue == nil {
			result := handleMissingValue(current.node, record, features, current.path, options)
			if result.Error != nil {
				return nil, nil, metrics, result.Error
			}
			if result.IsLeaf {
				return result.NextNode, result.Path, metrics, nil
			}
			stack = append(stack, stackItem{node: result.NextNode, depth: current.depth + 1, path: result.Path})
			continue
		}

		// Route based on feature type
		var nextNode *models.TreeNode
		var pathSegment string

		switch current.node.Feature.Type {
		case models.NumericFeature, models.DateFeature:
			numericValue, err := convertToFloat64(featureValue)
			if err != nil {
				return nil, nil, metrics, &models.ModelError{
					Op:     "traverse_node_iterative",
					Field:  "feature_value",
					Value:  fmt.Sprintf("%v", featureValue),
					Reason: fmt.Sprintf("cannot convert to numeric value: %v", err),
					Err:    err,
				}
			}

			if math.IsNaN(numericValue) || math.IsInf(numericValue, 0) {
				result := handleMissingValue(current.node, record, features, current.path, options)
				if result.Error != nil {
					return nil, nil, metrics, result.Error
				}
				if result.IsLeaf {
					return result.NextNode, result.Path, metrics, nil
				}
				stack = append(stack, stackItem{node: result.NextNode, depth: current.depth + 1, path: result.Path})
				continue
			}

			if numericValue <= current.node.Threshold {
				nextNode = current.node.Left
				pathSegment = fmt.Sprintf("%s <= %.6f", current.node.Feature.Name, current.node.Threshold)
			} else {
				nextNode = current.node.Right
				pathSegment = fmt.Sprintf("%s > %.6f", current.node.Feature.Name, current.node.Threshold)
			}

		case models.CategoricalFeature:
			categoryValue := fmt.Sprintf("%v", featureValue)
			var exists bool
			nextNode, exists = current.node.Categories[featureValue]
			if !exists {
				nextNode, exists = current.node.Categories[categoryValue]
			}
			pathSegment = fmt.Sprintf("%s = %s", current.node.Feature.Name, categoryValue)

			if !exists || nextNode == nil {
				result := handleMissingValue(current.node, record, features, current.path, options)
				if result.Error != nil {
					return nil, nil, metrics, result.Error
				}
				if result.IsLeaf {
					return result.NextNode, result.Path, metrics, nil
				}
				stack = append(stack, stackItem{node: result.NextNode, depth: current.depth + 1, path: result.Path})
				continue
			}

		default:
			return nil, nil, metrics, &models.ModelError{
				Op:     "traverse_node_iterative",
				Field:  "feature_type",
				Value:  string(current.node.Feature.Type),
				Reason: "unsupported feature type",
			}
		}

		// Update path if tracking is enabled
		newPath := current.path
		if options.IncludePath {
			newPath = append(newPath, pathSegment)
		}

		// Handle case where child doesn't exist
		if nextNode == nil {
			result := handleMissingValue(current.node, record, features, newPath, options)
			if result.Error != nil {
				return nil, nil, metrics, result.Error
			}
			if result.IsLeaf {
				return result.NextNode, result.Path, metrics, nil
			}
			stack = append(stack, stackItem{node: result.NextNode, depth: current.depth + 1, path: result.Path})
			continue
		}

		// Push next node to stack
		stack = append(stack, stackItem{node: nextNode, depth: current.depth + 1, path: newPath})
	}

	return nil, nil, metrics, &models.ModelError{
		Op:     "traverse_node_iterative",
		Reason: "traversal completed without reaching leaf node",
	}
}

// MissingValueResult contains the result of missing value handling
type MissingValueResult struct {
	NextNode *models.TreeNode
	Path     []string
	IsLeaf   bool
	Error    error
}

// handleMissingValue handles missing values for both recursive and iterative traversal
func handleMissingValue(node *models.TreeNode, record map[string]interface{}, features map[string]*models.Feature, path []string, options TraversalOptions) *MissingValueResult {
	result := &MissingValueResult{
		Path: path,
	}

	switch options.MissingValueStrategy {
	case FailOnMissing:
		result.Error = &models.ModelError{
			Op:     "handle_missing_value",
			Field:  "feature_value",
			Value:  node.Feature.Name,
			Reason: "missing value encountered and FailOnMissing strategy is enabled",
		}
		return result

	case UseDefaultPrediction:
		majorityClass := node.GetMajorityClass()
		if majorityClass == nil {
			majorityClass = "unknown"
		}

		if options.IncludePath {
			result.Path = append(result.Path, fmt.Sprintf("MISSING[%s] -> DEFAULT[%v]", node.Feature.Name, majorityClass))
		}

		// Create synthetic leaf node
		result.NextNode = &models.TreeNode{
			Type:              models.LeafNode,
			Prediction:        majorityClass,
			ClassDistribution: node.ClassDistribution,
			Samples:           node.Samples,
			Confidence:        node.Confidence,
			Impurity:          node.Impurity,
		}
		result.IsLeaf = true
		return result

	case UseMajorityClass:
		var bestChild *models.TreeNode
		maxSamples := -1

		// Check left and right children for numeric features
		if node.Left != nil && node.Left.Samples > maxSamples {
			maxSamples = node.Left.Samples
			bestChild = node.Left
		}
		if node.Right != nil && node.Right.Samples > maxSamples {
			maxSamples = node.Right.Samples
			bestChild = node.Right
		}

		// Check categorical children
		for _, child := range node.Categories {
			if child != nil && child.Samples > maxSamples {
				maxSamples = child.Samples
				bestChild = child
			}
		}

		if bestChild == nil {
			// No children available, fallback to UseDefaultPrediction
			fallbackOptions := TraversalOptions{
				MissingValueStrategy: UseDefaultPrediction,
				IncludePath:          options.IncludePath,
				MaxDepth:             options.MaxDepth,
			}
			return handleMissingValue(node, record, features, result.Path, fallbackOptions)
		}

		// Update path if tracking is enabled
		if options.IncludePath {
			result.Path = append(result.Path, fmt.Sprintf("MISSING[%s] -> MAJORITY_BRANCH", node.Feature.Name))
		}

		result.NextNode = bestChild
		result.IsLeaf = bestChild.IsLeaf()
		return result

	default:
		result.Error = &models.ModelError{
			Op:     "handle_missing_value",
			Field:  "missing_value_strategy",
			Value:  string(options.MissingValueStrategy),
			Reason: "unknown missing value strategy",
		}
		return result
	}
}

// traverseNodeRecursive recursively traverses the tree from the given node and collects metrics
func traverseNodeRecursive(node *models.TreeNode, record map[string]interface{}, features map[string]*models.Feature, path []string, options TraversalOptions, depth int, metrics *TraversalMetrics) (*models.TreeNode, []string, *TraversalMetrics, error) {
	// Update metrics
	if metrics != nil {
		metrics.NodesVisited++
		if depth > metrics.MaxDepthReached {
			metrics.MaxDepthReached = depth
		}
	}

	// Check depth limit to prevent infinite loops
	if depth > options.MaxDepth {
		return nil, nil, metrics, &models.ModelError{
			Op:     "traverse_node_recursive",
			Field:  "depth",
			Value:  fmt.Sprintf("%d", depth),
			Reason: fmt.Sprintf("traversal depth exceeded limit of %d", options.MaxDepth),
		}
	}

	// Base case: reached a leaf node
	if node.IsLeaf() {
		if options.IncludePath {
			path = append(path, fmt.Sprintf("LEAF[%v]", node.Prediction))
		}
		return node, path, metrics, nil
	}

	// Decision node: determine which child to traverse
	if node.Feature == nil {
		return nil, nil, metrics, &models.ModelError{
			Op:     "traverse_node_recursive",
			Field:  "feature",
			Reason: "decision node has no feature",
		}
	}

	// Get the feature value from the input record
	featureValue, exists := record[node.Feature.Name]

	// Handle missing values
	if !exists || featureValue == nil {
		result := handleMissingValue(node, record, features, path, options)
		if result.Error != nil {
			return nil, nil, metrics, result.Error
		}
		if result.IsLeaf {
			return result.NextNode, result.Path, metrics, nil
		}
		// Continue traversal with the selected node
		return traverseNodeRecursive(result.NextNode, record, features, result.Path, options, depth+1, metrics)
	}

	// Route based on feature type
	switch node.Feature.Type {
	case models.NumericFeature, models.DateFeature:
		return traverseNumericFeatureRecursive(node, featureValue, record, features, path, options, depth, metrics)
	case models.CategoricalFeature:
		return traverseCategoricalFeatureRecursive(node, featureValue, record, features, path, options, depth, metrics)
	default:
		return nil, nil, metrics, &models.ModelError{
			Op:     "traverse_node_recursive",
			Field:  "feature_type",
			Value:  string(node.Feature.Type),
			Reason: "unsupported feature type",
		}
	}
}

// traverseNumericFeatureRecursive handles traversal for numeric and date features
func traverseNumericFeatureRecursive(node *models.TreeNode, featureValue interface{}, record map[string]interface{}, features map[string]*models.Feature, path []string, options TraversalOptions, depth int, metrics *TraversalMetrics) (*models.TreeNode, []string, *TraversalMetrics, error) {
	// Convert feature value to float64
	numericValue, err := convertToFloat64(featureValue)
	if err != nil {
		return nil, nil, metrics, &models.ModelError{
			Op:     "traverse_numeric_feature_recursive",
			Field:  "feature_value",
			Value:  fmt.Sprintf("%v", featureValue),
			Reason: fmt.Sprintf("cannot convert to numeric value: %v", err),
			Err:    err,
		}
	}

	// Check for NaN or Inf
	if math.IsNaN(numericValue) || math.IsInf(numericValue, 0) {
		result := handleMissingValue(node, record, features, path, options)
		if result.Error != nil {
			return nil, nil, metrics, result.Error
		}
		if result.IsLeaf {
			return result.NextNode, result.Path, metrics, nil
		}
		return traverseNodeRecursive(result.NextNode, record, features, result.Path, options, depth+1, metrics)
	}

	// Determine which child to traverse based on threshold
	var nextNode *models.TreeNode
	var pathSegment string

	if numericValue <= node.Threshold {
		nextNode = node.Left
		pathSegment = fmt.Sprintf("%s <= %.6f", node.Feature.Name, node.Threshold)
	} else {
		nextNode = node.Right
		pathSegment = fmt.Sprintf("%s > %.6f", node.Feature.Name, node.Threshold)
	}

	// Update path if tracking is enabled
	if options.IncludePath {
		path = append(path, pathSegment)
	}

	// Handle case where child doesn't exist
	if nextNode == nil {
		result := handleMissingValue(node, record, features, path, options)
		if result.Error != nil {
			return nil, nil, metrics, result.Error
		}
		if result.IsLeaf {
			return result.NextNode, result.Path, metrics, nil
		}
		return traverseNodeRecursive(result.NextNode, record, features, result.Path, options, depth+1, metrics)
	}

	// Recursively traverse the selected child
	return traverseNodeRecursive(nextNode, record, features, path, options, depth+1, metrics)
}

// traverseCategoricalFeatureRecursive handles traversal for categorical features
func traverseCategoricalFeatureRecursive(node *models.TreeNode, featureValue interface{}, record map[string]interface{}, features map[string]*models.Feature, path []string, options TraversalOptions, depth int, metrics *TraversalMetrics) (*models.TreeNode, []string, *TraversalMetrics, error) {
	// Convert feature value to string for comparison
	categoryValue := fmt.Sprintf("%v", featureValue)

	// Look up the child node for this category
	nextNode, exists := node.Categories[featureValue]
	if !exists {
		// Try string version as well for compatibility
		nextNode, exists = node.Categories[categoryValue]
	}

	// Update path if tracking is enabled
	if options.IncludePath {
		path = append(path, fmt.Sprintf("%s = %s", node.Feature.Name, categoryValue))
	}

	// Handle case where category doesn't exist in tree
	if !exists || nextNode == nil {
		result := handleMissingValue(node, record, features, path, options)
		if result.Error != nil {
			return nil, nil, metrics, result.Error
		}
		if result.IsLeaf {
			return result.NextNode, result.Path, metrics, nil
		}
		return traverseNodeRecursive(result.NextNode, record, features, result.Path, options, depth+1, metrics)
	}

	// Recursively traverse the selected child
	return traverseNodeRecursive(nextNode, record, features, path, options, depth+1, metrics)
}

// convertToFloat64 converts various numeric types to float64
func convertToFloat64(value interface{}) (float64, error) {
	switch v := value.(type) {
	case float64:
		return v, nil
	case float32:
		return float64(v), nil
	case int:
		return float64(v), nil
	case int32:
		return float64(v), nil
	case int64:
		return float64(v), nil
	case uint:
		return float64(v), nil
	case uint32:
		return float64(v), nil
	case uint64:
		return float64(v), nil
	case string:
		// Try to parse string as number
		if parsed, err := strconv.ParseFloat(v, 64); err == nil {
			return parsed, nil
		}
		return 0, fmt.Errorf("cannot parse string '%s' as number", v)
	default:
		return 0, fmt.Errorf("unsupported type %T", value)
	}
}

// PredictBatch makes predictions for multiple input records efficiently
func PredictBatch(tree *models.DecisionTree, records []map[string]interface{}, options TraversalOptions) ([]*TraversalResult, error) {
	if tree == nil {
		return nil, &models.ModelError{
			Op:     "predict_batch",
			Field:  "tree",
			Reason: "tree cannot be nil",
		}
	}

	if records == nil {
		return nil, &models.ModelError{
			Op:     "predict_batch",
			Field:  "records",
			Reason: "records cannot be nil",
		}
	}

	results := make([]*TraversalResult, len(records))
	for i, record := range records {
		result, err := TraverseTree(tree, record, options)
		if err != nil {
			return nil, &models.ModelError{
				Op:     "predict_batch",
				Field:  "record",
				Value:  fmt.Sprintf("index_%d", i),
				Reason: fmt.Sprintf("failed to predict record at index %d: %v", i, err),
				Err:    err,
			}
		}
		results[i] = result
	}

	return results, nil
}

// ExtractPredictions extracts just the prediction values from traversal results
func ExtractPredictions(results []*TraversalResult) []interface{} {
	predictions := make([]interface{}, len(results))
	for i, result := range results {
		if result != nil {
			predictions[i] = result.Prediction
		}
	}
	return predictions
}

// AnalyzeConfidenceStats analyzes confidence values from traversal results and computes statistical measures.
// This function does not calculate confidence values - it performs statistical analysis on pre-existing
// confidence values that were retrieved from tree leaf nodes during traversal.
//
// The function computes:
// - Mean, Min, Max confidence values
// - Standard deviation of confidence values
// - Count of low confidence predictions (< 0.5)
// - Count of high confidence predictions (>= 0.8)
//
// Returns statistical measures that can be used for model quality assessment and monitoring.
type ConfidenceStats struct {
	Mean      float64 `json:"mean"`
	Min       float64 `json:"min"`
	Max       float64 `json:"max"`
	StdDev    float64 `json:"std_dev"`
	LowCount  int     `json:"low_count"`  // Count of predictions with confidence < 0.5
	HighCount int     `json:"high_count"` // Count of predictions with confidence >= 0.8
}

// AnalyzeConfidenceStats analyzes confidence values from traversal results and computes statistical measures
func AnalyzeConfidenceStats(results []*TraversalResult) *ConfidenceStats {
	if len(results) == 0 {
		return &ConfidenceStats{}
	}

	stats := &ConfidenceStats{
		Min: 1.0,
		Max: 0.0,
	}

	sum := 0.0
	sumSquares := 0.0

	for _, result := range results {
		if result == nil {
			continue
		}

		confidence := result.Confidence
		sum += confidence
		sumSquares += confidence * confidence

		if confidence < stats.Min {
			stats.Min = confidence
		}
		if confidence > stats.Max {
			stats.Max = confidence
		}

		if confidence < 0.5 {
			stats.LowCount++
		}
		if confidence >= 0.8 {
			stats.HighCount++
		}
	}

	n := float64(len(results))
	stats.Mean = sum / n

	// Calculate standard deviation
	variance := (sumSquares / n) - (stats.Mean * stats.Mean)
	if variance > 0 {
		stats.StdDev = math.Sqrt(variance)
	}

	return stats
}
