package prediction

import (
	"math"
	"strings"
	"testing"
	"time"

	"github.com/berrijam/mulberri/pkg/models"
)

// Helper function to create a simple tree for testing
func createSimpleTree() (*models.DecisionTree, error) {
	tree, err := models.NewDecisionTree(models.CategoricalFeature, 5, 2)
	if err != nil {
		return nil, err
	}

	// Add features
	ageFeature, err := tree.AddFeature("age", models.NumericFeature)
	if err != nil {
		return nil, err
	}

	sexFeature, err := tree.AddFeature("sex", models.CategoricalFeature)
	if err != nil {
		return nil, err
	}

	// Create tree structure:
	//                 age <= 30
	//                /         \
	//          sex=male       LEAF: approved
	//            /    \
	//    LEAF: denied  LEAF: approved

	// Root node: age <= 30
	root, err := models.NewDecisionNode(ageFeature, 30.0)
	if err != nil {
		return nil, err
	}
	root.Samples = 100
	root.Confidence = 0.7
	root.Impurity = 0.4

	// Left child: sex categorical split
	leftChild, err := models.NewCategoricalDecisionNode(sexFeature)
	if err != nil {
		return nil, err
	}
	leftChild.Samples = 60
	leftChild.Confidence = 0.6
	leftChild.Impurity = 0.5

	// Right child: leaf node
	rightChild, err := models.NewLeafNode("approved", map[interface{}]int{"approved": 35, "denied": 5}, 40)
	if err != nil {
		return nil, err
	}

	// Left-left child: leaf node (male -> denied)
	leftLeftChild, err := models.NewLeafNode("denied", map[interface{}]int{"denied": 25, "approved": 5}, 30)
	if err != nil {
		return nil, err
	}

	// Left-right child: leaf node (female -> approved)
	leftRightChild, err := models.NewLeafNode("approved", map[interface{}]int{"approved": 25, "denied": 5}, 30)
	if err != nil {
		return nil, err
	}

	// Set up tree structure
	root.Left = leftChild
	root.Right = rightChild

	err = leftChild.SetChild("male", leftLeftChild)
	if err != nil {
		return nil, err
	}

	err = leftChild.SetChild("female", leftRightChild)
	if err != nil {
		return nil, err
	}

	tree.Root = root
	return tree, nil
}

// Helper function to create a deep tree for performance testing
func createDeepTree(depth int) (*models.DecisionTree, error) {
	tree, err := models.NewDecisionTree(models.CategoricalFeature, depth+5, 1)
	if err != nil {
		return nil, err
	}

	feature, err := tree.AddFeature("x", models.NumericFeature)
	if err != nil {
		return nil, err
	}

	// Create a chain of nodes
	var root *models.TreeNode
	var current *models.TreeNode

	for i := 0; i < depth; i++ {
		node, err := models.NewDecisionNode(feature, float64(i))
		if err != nil {
			return nil, err
		}
		node.Samples = 100 - i
		node.Confidence = 0.5
		node.Impurity = 0.3

		if root == nil {
			root = node
			current = node
		} else {
			current.Left = node
			current = node
		}
	}

	// Add final leaf
	if current != nil {
		leaf, err := models.NewLeafNode("final", map[interface{}]int{"final": 10}, 10)
		if err != nil {
			return nil, err
		}
		current.Left = leaf
	}

	tree.Root = root
	return tree, nil
}

func TestTraverseTreeSimple(t *testing.T) {
	tree, err := createSimpleTree()
	if err != nil {
		t.Fatalf("Failed to create test tree: %v", err)
	}

	tests := []struct {
		name           string
		record         map[string]interface{}
		expectedResult string
	}{
		{
			name:           "Young male should be denied",
			record:         map[string]interface{}{"age": 25.0, "sex": "male"},
			expectedResult: "denied",
		},
		{
			name:           "Young female should be approved",
			record:         map[string]interface{}{"age": 25.0, "sex": "female"},
			expectedResult: "approved",
		},
		{
			name:           "Old person should be approved",
			record:         map[string]interface{}{"age": 35.0, "sex": "male"},
			expectedResult: "approved",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := TraverseTreeSimple(tree, tt.record)
			if err != nil {
				t.Fatalf("TraverseTreeSimple failed: %v", err)
			}

			if result.Prediction != tt.expectedResult {
				t.Errorf("Expected prediction %v, got %v", tt.expectedResult, result.Prediction)
			}

			if result.Confidence <= 0 || result.Confidence > 1 {
				t.Errorf("Invalid confidence value: %f", result.Confidence)
			}

			if result.ClassDistribution == nil {
				t.Error("Class distribution should not be nil")
			}

			// Test probabilities
			if result.Probabilities == nil {
				t.Error("Probabilities should not be nil")
			}

			// Check if probabilities sum to approximately 1
			sum := 0.0
			for _, prob := range result.Probabilities {
				sum += prob
			}
			if math.Abs(sum-1.0) > 0.001 {
				t.Errorf("Probabilities should sum to 1, got %f", sum)
			}
		})
	}
}

func TestTraverseTreeWithPath(t *testing.T) {
	tree, err := createSimpleTree()
	if err != nil {
		t.Fatalf("Failed to create test tree: %v", err)
	}

	options := TraversalOptions{
		MissingValueStrategy: UseMajorityClass,
		IncludePath:          true,
		MaxDepth:             100,
		Strategy:             RecursiveTraversal,
	}

	record := map[string]interface{}{"age": 25.0, "sex": "male"}
	result, err := TraverseTree(tree, record, options)
	if err != nil {
		t.Fatalf("TraverseTree failed: %v", err)
	}

	if len(result.Path) == 0 {
		t.Error("Path should not be empty when IncludePath is true")
	}

	expectedPathLength := 3 // age decision + sex decision + leaf
	if len(result.Path) != expectedPathLength {
		t.Errorf("Expected path length %d, got %d. Path: %v", expectedPathLength, len(result.Path), result.Path)
	}

	// Test rule format
	if result.RulePath == "" {
		t.Error("Rule should not be empty when IncludePath is true")
	}

	// Rule should contain conditions joined with &
	if !strings.Contains(result.RulePath, "&") {
		t.Error("Rule should contain & to join conditions")
	}

	// Rule should contain -->
	if !strings.Contains(result.RulePath, "-->") {
		t.Error("Rule should contain --> before leaf prediction")
	}

	// Rule should be wrapped in brackets
	if !strings.HasPrefix(result.RulePath, "[") || !strings.HasSuffix(result.RulePath, "]") {
		t.Error("Rule should be wrapped in brackets")
	}
}

func TestTraversalStrategies(t *testing.T) {
	tree, err := createDeepTree(10)
	if err != nil {
		t.Fatalf("Failed to create deep tree: %v", err)
	}

	record := map[string]interface{}{"x": -1.0}

	strategies := []TraversalStrategy{
		RecursiveTraversal,
		IterativeTraversal,
		AutoStrategy,
	}

	for _, strategy := range strategies {
		t.Run(string(strategy), func(t *testing.T) {
			options := TraversalOptions{
				MissingValueStrategy: UseMajorityClass,
				IncludePath:          false,
				MaxDepth:             1000,
				Strategy:             strategy,
			}

			result, err := TraverseTree(tree, record, options)
			if err != nil {
				t.Fatalf("TraverseTree with %s strategy failed: %v", strategy, err)
			}

			if result.Prediction != "final" {
				t.Errorf("Expected 'final', got %v with strategy %s", result.Prediction, strategy)
			}
		})
	}
}

func TestTraverseTreeWithMetrics(t *testing.T) {
	tree, err := createSimpleTree()
	if err != nil {
		t.Fatalf("Failed to create test tree: %v", err)
	}

	options := TraversalOptions{
		MissingValueStrategy: UseMajorityClass,
		IncludePath:          true,
		MaxDepth:             100,
		Strategy:             RecursiveTraversal,
		CollectMetrics:       true,
	}

	record := map[string]interface{}{"age": 25.0, "sex": "male"}
	result, metrics, err := TraverseTreeWithMetrics(tree, record, options)
	if err != nil {
		t.Fatalf("TraverseTreeWithMetrics failed: %v", err)
	}

	if result == nil {
		t.Error("Result should not be nil")
	}

	if metrics == nil {
		t.Error("Metrics should not be nil when CollectMetrics is true")
	}

	if metrics != nil && metrics.Strategy != string(RecursiveTraversal) {
		t.Errorf("Expected strategy %s, got %s", RecursiveTraversal, metrics.Strategy)
	}
}

func TestIterativeTraversalDeepTree(t *testing.T) {
	// Create a very deep tree to test iterative traversal
	depth := 100
	tree, err := createDeepTree(depth)
	if err != nil {
		t.Fatalf("Failed to create deep tree: %v", err)
	}

	options := TraversalOptions{
		MissingValueStrategy: UseMajorityClass,
		IncludePath:          true,
		MaxDepth:             1000,
		Strategy:             IterativeTraversal,
	}

	record := map[string]interface{}{"x": -1.0}
	result, err := TraverseTree(tree, record, options)
	if err != nil {
		t.Fatalf("Iterative traversal failed on deep tree: %v", err)
	}

	if result.Prediction != "final" {
		t.Errorf("Expected 'final', got %v", result.Prediction)
	}

	// Path should be long for deep tree
	if len(result.Path) < depth {
		t.Errorf("Expected path length >= %d, got %d", depth, len(result.Path))
	}
}

func TestAutoStrategySelection(t *testing.T) {
	// Test that AutoStrategy chooses iterative for deep trees
	deepTree, err := createDeepTree(RECURSIVE_THRESHOLD + 10)
	if err != nil {
		t.Fatalf("Failed to create deep tree: %v", err)
	}

	options := TraversalOptions{
		Strategy: AutoStrategy,
		MaxDepth: 1000,
	}

	record := map[string]interface{}{"x": -1.0}
	result, err := TraverseTree(deepTree, record, options)
	if err != nil {
		t.Fatalf("Auto strategy traversal failed: %v", err)
	}

	if result.Prediction != "final" {
		t.Errorf("Expected 'final', got %v", result.Prediction)
	}

	// Test that AutoStrategy chooses recursive for shallow trees
	shallowTree, err := createSimpleTree()
	if err != nil {
		t.Fatalf("Failed to create shallow tree: %v", err)
	}

	result2, err := TraverseTree(shallowTree, map[string]interface{}{"age": 25.0, "sex": "male"}, options)
	if err != nil {
		t.Fatalf("Auto strategy traversal failed on shallow tree: %v", err)
	}

	if result2.Prediction != "denied" {
		t.Errorf("Expected 'denied', got %v", result2.Prediction)
	}
}

func TestMetricsConsistency(t *testing.T) {
	tree, err := createSimpleTree()
	if err != nil {
		t.Fatalf("Failed to create test tree: %v", err)
	}

	record := map[string]interface{}{"age": 25.0, "sex": "male"}

	// Test recursive traversal metrics
	recursiveOptions := TraversalOptions{
		CollectMetrics: true,
		Strategy:       RecursiveTraversal,
	}

	result, metrics, err := TraverseTreeWithMetrics(tree, record, recursiveOptions)
	if err != nil {
		t.Fatalf("TraverseTreeWithMetrics failed for recursive: %v", err)
	}

	if metrics == nil {
		t.Error("Metrics should not be nil when CollectMetrics is true")
	}

	// Test iterative traversal metrics
	iterativeOptions := TraversalOptions{
		CollectMetrics: true,
		Strategy:       IterativeTraversal,
	}

	result2, metrics2, err := TraverseTreeWithMetrics(tree, record, iterativeOptions)
	if err != nil {
		t.Fatalf("TraverseTreeWithMetrics failed for iterative: %v", err)
	}

	if metrics2 == nil {
		t.Error("Metrics should not be nil when CollectMetrics is true")
	}

	// Both should reach the same prediction
	if result.Prediction != result2.Prediction {
		t.Errorf("Both strategies should reach same prediction: %v vs %v", result.Prediction, result2.Prediction)
	}
}

func TestInputValidation(t *testing.T) {
	tree, err := createSimpleTree()
	if err != nil {
		t.Fatalf("Failed to create test tree: %v", err)
	}

	tests := []struct {
		name    string
		record  map[string]interface{}
		wantErr bool
	}{
		{
			name:    "Valid record",
			record:  map[string]interface{}{"age": 25.0, "sex": "male"},
			wantErr: false,
		},
		{
			name:    "Invalid age type",
			record:  map[string]interface{}{"age": "not_a_number", "sex": "male"},
			wantErr: true,
		},
		{
			name:    "Missing features handled gracefully",
			record:  map[string]interface{}{"age": 25.0},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := TraverseTreeSimple(tree, tt.record)

			if tt.wantErr && err == nil {
				t.Error("Expected error but got none")
			}

			if !tt.wantErr && err != nil {
				t.Fatalf("Unexpected error: %v", err)
			}

			if !tt.wantErr && result == nil {
				t.Error("Result should not be nil for valid case")
			}
		})
	}
}

func TestSingleNodeTreeDetection(t *testing.T) {
	tree, err := models.NewDecisionTree(models.CategoricalFeature, 1, 1)
	if err != nil {
		t.Fatalf("Failed to create tree: %v", err)
	}

	// Single leaf node tree
	root, err := models.NewLeafNode("default", map[interface{}]int{"default": 100}, 100)
	if err != nil {
		t.Fatalf("Failed to create root leaf: %v", err)
	}

	tree.Root = root

	if !isSingleNodeTree(tree) {
		t.Error("Should detect single node tree")
	}

	// Test with multi-node tree
	multiTree, err := createSimpleTree()
	if err != nil {
		t.Fatalf("Failed to create multi-node tree: %v", err)
	}

	if isSingleNodeTree(multiTree) {
		t.Error("Should not detect multi-node tree as single node")
	}
}

func TestProbabilityNormalization(t *testing.T) {
	classDistribution := map[interface{}]int{
		"approved": 70,
		"denied":   30,
	}

	probabilities := normalizeClassDistribution(classDistribution)

	expectedApproved := 0.7
	expectedDenied := 0.3

	if math.Abs(probabilities["approved"]-expectedApproved) > 0.001 {
		t.Errorf("Expected approved probability %f, got %f", expectedApproved, probabilities["approved"])
	}

	if math.Abs(probabilities["denied"]-expectedDenied) > 0.001 {
		t.Errorf("Expected denied probability %f, got %f", expectedDenied, probabilities["denied"])
	}

	// Test empty distribution
	emptyDistribution := map[interface{}]int{}
	emptyProbs := normalizeClassDistribution(emptyDistribution)
	if len(emptyProbs) != 0 {
		t.Error("Empty distribution should result in empty probabilities")
	}
}

func TestTreeDepthCaching(t *testing.T) {
	// Clear cache before test
	ClearDepthCache()

	tree, err := createDeepTree(10)
	if err != nil {
		t.Fatalf("Failed to create deep tree: %v", err)
	}

	// First call should compute and cache
	depth1 := getTreeDepth(tree)
	if depth1 < 10 {
		t.Errorf("Expected depth >= 10, got %d", depth1)
	}

	// Second call should use cache (same result)
	depth2 := getTreeDepth(tree)
	if depth1 != depth2 {
		t.Errorf("Cached depth mismatch: first=%d, second=%d", depth1, depth2)
	}

	// Test with nil tree
	depth3 := getTreeDepth(nil)
	if depth3 != 0 {
		t.Errorf("Expected depth 0 for nil tree, got %d", depth3)
	}

	// Test cache clearing
	ClearDepthCache()
	depth4 := getTreeDepth(tree)
	if depth4 != depth1 {
		t.Errorf("Depth should be same after cache clear: expected=%d, got=%d", depth1, depth4)
	}
}

func TestDepthCachingPerformance(t *testing.T) {
	// Clear cache before test
	ClearDepthCache()

	tree, err := createDeepTree(50)
	if err != nil {
		t.Fatalf("Failed to create deep tree: %v", err)
	}

	// Time the first call (should compute)
	start1 := time.Now()
	depth1 := getTreeDepth(tree)
	elapsed1 := time.Since(start1)

	// Time the second call (should use cache)
	start2 := time.Now()
	depth2 := getTreeDepth(tree)
	elapsed2 := time.Since(start2)

	if depth1 != depth2 {
		t.Errorf("Depths should match: first=%d, second=%d", depth1, depth2)
	}

	// Cached call should be significantly faster
	if elapsed2 > elapsed1/2 {
		t.Logf("Warning: Cached call (%v) not significantly faster than first call (%v)", elapsed2, elapsed1)
	}

	t.Logf("First call: %v, Cached call: %v, Speedup: %.2fx", elapsed1, elapsed2, float64(elapsed1)/float64(elapsed2))
}

func TestAutoStrategyWithCaching(t *testing.T) {
	// Clear cache before test
	ClearDepthCache()

	// Test that auto strategy uses cached depth
	deepTree, err := createDeepTree(RECURSIVE_THRESHOLD + 5)
	if err != nil {
		t.Fatalf("Failed to create deep tree: %v", err)
	}

	shallowTree, err := createSimpleTree()
	if err != nil {
		t.Fatalf("Failed to create shallow tree: %v", err)
	}

	options := TraversalOptions{
		Strategy: AutoStrategy,
		MaxDepth: 1000,
	}

	// Test deep tree uses iterative
	record := map[string]interface{}{"x": -1.0}
	_, err = TraverseTree(deepTree, record, options)
	if err != nil {
		t.Fatalf("Deep tree traversal failed: %v", err)
	}

	// Test shallow tree uses recursive
	record2 := map[string]interface{}{"age": 25.0, "sex": "male"}
	_, err = TraverseTree(shallowTree, record2, options)
	if err != nil {
		t.Fatalf("Shallow tree traversal failed: %v", err)
	}

	// Verify both trees are now cached
	cachedDeepDepth := getTreeDepth(deepTree)
	cachedShallowDepth := getTreeDepth(shallowTree)

	if cachedDeepDepth <= RECURSIVE_THRESHOLD {
		t.Errorf("Deep tree depth should be > %d, got %d", RECURSIVE_THRESHOLD, cachedDeepDepth)
	}

	if cachedShallowDepth > RECURSIVE_THRESHOLD {
		t.Errorf("Shallow tree depth should be <= %d, got %d", RECURSIVE_THRESHOLD, cachedShallowDepth)
	}
}

func TestNumericFeatureThresholds(t *testing.T) {
	tree, err := models.NewDecisionTree(models.CategoricalFeature, 3, 1)
	if err != nil {
		t.Fatalf("Failed to create tree: %v", err)
	}

	feature, err := tree.AddFeature("value", models.NumericFeature)
	if err != nil {
		t.Fatalf("Failed to add feature: %v", err)
	}

	// Create simple threshold test
	root, err := models.NewDecisionNode(feature, 10.0)
	if err != nil {
		t.Fatalf("Failed to create root: %v", err)
	}

	leftLeaf, err := models.NewLeafNode("low", map[interface{}]int{"low": 10}, 10)
	if err != nil {
		t.Fatalf("Failed to create left leaf: %v", err)
	}

	rightLeaf, err := models.NewLeafNode("high", map[interface{}]int{"high": 10}, 10)
	if err != nil {
		t.Fatalf("Failed to create right leaf: %v", err)
	}

	root.Left = leftLeaf
	root.Right = rightLeaf
	tree.Root = root

	tests := []struct {
		name     string
		value    interface{}
		expected string
	}{
		{"Value exactly at threshold", 10.0, "low"},
		{"Value below threshold", 5.0, "low"},
		{"Value above threshold", 15.0, "high"},
		{"Integer value below", 8, "low"},
		{"Integer value above", 12, "high"},
		{"String numeric below", "9.5", "low"},
		{"String numeric above", "10.1", "high"},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			record := map[string]interface{}{"value": tt.value}
			result, err := TraverseTreeSimple(tree, record)
			if err != nil {
				t.Fatalf("TraverseTreeSimple failed: %v", err)
			}

			if result.Prediction != tt.expected {
				t.Errorf("Expected %v, got %v for value %v", tt.expected, result.Prediction, tt.value)
			}
		})
	}
}

func TestCategoricalFeatures(t *testing.T) {
	tree, err := models.NewDecisionTree(models.CategoricalFeature, 3, 1)
	if err != nil {
		t.Fatalf("Failed to create tree: %v", err)
	}

	feature, err := tree.AddFeature("color", models.CategoricalFeature)
	if err != nil {
		t.Fatalf("Failed to add feature: %v", err)
	}

	// Create categorical split
	root, err := models.NewCategoricalDecisionNode(feature)
	if err != nil {
		t.Fatalf("Failed to create root: %v", err)
	}

	redLeaf, err := models.NewLeafNode("stop", map[interface{}]int{"stop": 10}, 10)
	if err != nil {
		t.Fatalf("Failed to create red leaf: %v", err)
	}

	greenLeaf, err := models.NewLeafNode("go", map[interface{}]int{"go": 10}, 10)
	if err != nil {
		t.Fatalf("Failed to create green leaf: %v", err)
	}

	yellowLeaf, err := models.NewLeafNode("caution", map[interface{}]int{"caution": 10}, 10)
	if err != nil {
		t.Fatalf("Failed to create yellow leaf: %v", err)
	}

	err = root.SetChild("red", redLeaf)
	if err != nil {
		t.Fatalf("Failed to set red child: %v", err)
	}

	err = root.SetChild("green", greenLeaf)
	if err != nil {
		t.Fatalf("Failed to set green child: %v", err)
	}

	err = root.SetChild("yellow", yellowLeaf)
	if err != nil {
		t.Fatalf("Failed to set yellow child: %v", err)
	}

	tree.Root = root

	tests := []struct {
		name     string
		color    interface{}
		expected string
	}{
		{"Red color", "red", "stop"},
		{"Green color", "green", "go"},
		{"Yellow color", "yellow", "caution"},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			record := map[string]interface{}{"color": tt.color}
			result, err := TraverseTreeSimple(tree, record)

			if err != nil {
				t.Fatalf("TraverseTreeSimple failed: %v", err)
			}

			if result.Prediction != tt.expected {
				t.Errorf("Expected %v, got %v for color %v", tt.expected, result.Prediction, tt.color)
			}
		})
	}

	// Test unknown category handling separately
	t.Run("Unknown category handling", func(t *testing.T) {
		record := map[string]interface{}{"color": 123}
		result, err := TraverseTreeSimple(tree, record)

		if err != nil {
			t.Fatalf("TraverseTreeSimple failed: %v", err)
		}

		// Should handle unknown category gracefully by using majority class strategy
		if result == nil {
			t.Error("Result should not be nil for unknown category")
		} else if result.Prediction == nil {
			t.Error("Prediction should not be nil for unknown category")
		}
	})
}

func TestMissingValueHandling(t *testing.T) {
	tree, err := createSimpleTree()
	if err != nil {
		t.Fatalf("Failed to create test tree: %v", err)
	}

	tests := []struct {
		name     string
		record   map[string]interface{}
		strategy MissingValueStrategy
		wantErr  bool
	}{
		{
			name:     "Missing age with FailOnMissing",
			record:   map[string]interface{}{"sex": "male"},
			strategy: FailOnMissing,
			wantErr:  true,
		},
		{
			name:     "Missing age with UseMajorityClass",
			record:   map[string]interface{}{"sex": "male"},
			strategy: UseMajorityClass,
			wantErr:  false,
		},
		{
			name:     "Missing age with UseDefaultPrediction",
			record:   map[string]interface{}{"sex": "male"},
			strategy: UseDefaultPrediction,
			wantErr:  false,
		},
		{
			name:     "Missing sex with UseMajorityClass",
			record:   map[string]interface{}{"age": 25.0},
			strategy: UseMajorityClass,
			wantErr:  false,
		},
		{
			name:     "Nil value with UseMajorityClass",
			record:   map[string]interface{}{"age": nil, "sex": "male"},
			strategy: UseMajorityClass,
			wantErr:  false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			options := TraversalOptions{
				MissingValueStrategy: tt.strategy,
				IncludePath:          false,
				MaxDepth:             100,
			}

			result, err := TraverseTree(tree, tt.record, options)

			if tt.wantErr {
				if err == nil {
					t.Error("Expected error but got none")
				}
				return
			}

			if err != nil {
				t.Fatalf("Unexpected error: %v", err)
			}

			if result == nil {
				t.Error("Result should not be nil")
			} else if result.Prediction == nil {
				t.Error("Prediction should not be nil")
			}
		})
	}
}

func TestSpecialNumericValues(t *testing.T) {
	tree, err := models.NewDecisionTree(models.CategoricalFeature, 3, 1)
	if err != nil {
		t.Fatalf("Failed to create tree: %v", err)
	}

	feature, err := tree.AddFeature("value", models.NumericFeature)
	if err != nil {
		t.Fatalf("Failed to add feature: %v", err)
	}

	root, err := models.NewDecisionNode(feature, 10.0)
	if err != nil {
		t.Fatalf("Failed to create root: %v", err)
	}

	leftLeaf, err := models.NewLeafNode("low", map[interface{}]int{"low": 10}, 10)
	if err != nil {
		t.Fatalf("Failed to create left leaf: %v", err)
	}

	rightLeaf, err := models.NewLeafNode("high", map[interface{}]int{"high": 10}, 10)
	if err != nil {
		t.Fatalf("Failed to create right leaf: %v", err)
	}

	root.Left = leftLeaf
	root.Right = rightLeaf
	tree.Root = root

	tests := []struct {
		name    string
		value   interface{}
		wantErr bool
	}{
		{"NaN value", math.NaN(), false},           // Should be handled as missing
		{"Positive infinity", math.Inf(1), false},  // Should be handled as missing
		{"Negative infinity", math.Inf(-1), false}, // Should be handled as missing
		{"Invalid string", "not_a_number", true},
		{"Empty string", "", true},
	}

	options := TraversalOptions{
		MissingValueStrategy: UseMajorityClass,
		IncludePath:          false,
		MaxDepth:             100,
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			record := map[string]interface{}{"value": tt.value}
			result, err := TraverseTree(tree, record, options)

			if tt.wantErr && err == nil {
				t.Error("Expected error but got none")
			}

			if !tt.wantErr && err != nil {
				t.Fatalf("Unexpected error: %v", err)
			}

			if !tt.wantErr && result == nil {
				t.Error("Result should not be nil for valid case")
			}
		})
	}
}

func TestSingleNodeTree(t *testing.T) {
	tree, err := models.NewDecisionTree(models.CategoricalFeature, 1, 1)
	if err != nil {
		t.Fatalf("Failed to create tree: %v", err)
	}

	// Single leaf node tree
	root, err := models.NewLeafNode("default", map[interface{}]int{"default": 100}, 100)
	if err != nil {
		t.Fatalf("Failed to create root leaf: %v", err)
	}

	tree.Root = root

	record := map[string]interface{}{"any_feature": "any_value"}
	result, err := TraverseTreeSimple(tree, record)
	if err != nil {
		t.Fatalf("TraverseTreeSimple failed: %v", err)
	}

	if result.Prediction != "default" {
		t.Errorf("Expected 'default', got %v", result.Prediction)
	}

	if result.Confidence <= 0 {
		t.Errorf("Expected positive confidence, got %f", result.Confidence)
	}
}

func TestUnbalancedTree(t *testing.T) {
	tree, err := models.NewDecisionTree(models.CategoricalFeature, 10, 1)
	if err != nil {
		t.Fatalf("Failed to create tree: %v", err)
	}

	feature, err := tree.AddFeature("x", models.NumericFeature)
	if err != nil {
		t.Fatalf("Failed to add feature: %v", err)
	}

	// Create unbalanced tree: root has only left children in a chain
	root, err := models.NewDecisionNode(feature, 10.0)
	if err != nil {
		t.Fatalf("Failed to create root: %v", err)
	}

	level1, err := models.NewDecisionNode(feature, 5.0)
	if err != nil {
		t.Fatalf("Failed to create level1: %v", err)
	}

	level2, err := models.NewDecisionNode(feature, 2.0)
	if err != nil {
		t.Fatalf("Failed to create level2: %v", err)
	}

	leaf, err := models.NewLeafNode("deep", map[interface{}]int{"deep": 5}, 5)
	if err != nil {
		t.Fatalf("Failed to create leaf: %v", err)
	}

	rightLeaf, err := models.NewLeafNode("shallow", map[interface{}]int{"shallow": 50}, 50)
	if err != nil {
		t.Fatalf("Failed to create right leaf: %v", err)
	}

	// Build unbalanced structure
	root.Left = level1
	root.Right = rightLeaf
	level1.Left = level2
	level2.Left = leaf

	tree.Root = root

	tests := []struct {
		name     string
		value    float64
		expected string
	}{
		{"Go right at root", 15.0, "shallow"},
		{"Go deep left", 1.0, "deep"},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			record := map[string]interface{}{"x": tt.value}
			result, err := TraverseTreeSimple(tree, record)
			if err != nil {
				t.Fatalf("TraverseTreeSimple failed: %v", err)
			}

			if result.Prediction != tt.expected {
				t.Errorf("Expected %v, got %v", tt.expected, result.Prediction)
			}
		})
	}
}

func TestDeepTreeTraversal(t *testing.T) {
	depth := 100
	tree, err := createDeepTree(depth)
	if err != nil {
		t.Fatalf("Failed to create deep tree: %v", err)
	}

	// Test that it completes without stack overflow
	record := map[string]interface{}{"x": -1.0} // Will go left all the way down
	result, err := TraverseTreeSimple(tree, record)
	if err != nil {
		t.Fatalf("TraverseTreeSimple failed on deep tree: %v", err)
	}

	if result.Prediction != "final" {
		t.Errorf("Expected 'final', got %v", result.Prediction)
	}
}

func TestMaxDepthLimit(t *testing.T) {
	depth := 10
	tree, err := createDeepTree(depth)
	if err != nil {
		t.Fatalf("Failed to create deep tree: %v", err)
	}

	// Set max depth lower than tree depth
	options := TraversalOptions{
		MissingValueStrategy: UseMajorityClass,
		IncludePath:          false,
		MaxDepth:             5, // Less than actual tree depth
	}

	record := map[string]interface{}{"x": -1.0}
	_, err = TraverseTree(tree, record, options)
	if err == nil {
		t.Error("Expected error due to max depth limit, but got none")
	}
}

func TestPredictBatch(t *testing.T) {
	tree, err := createSimpleTree()
	if err != nil {
		t.Fatalf("Failed to create test tree: %v", err)
	}

	records := []map[string]interface{}{
		{"age": 25.0, "sex": "male"},
		{"age": 25.0, "sex": "female"},
		{"age": 35.0, "sex": "male"},
	}

	expectedResults := []string{"denied", "approved", "approved"}

	results, err := PredictBatch(tree, records, DefaultTraversalOptions())
	if err != nil {
		t.Fatalf("PredictBatch failed: %v", err)
	}

	if len(results) != len(records) {
		t.Errorf("Expected %d results, got %d", len(records), len(results))
	}

	for i, result := range results {
		if result.Prediction != expectedResults[i] {
			t.Errorf("Record %d: expected %v, got %v", i, expectedResults[i], result.Prediction)
		}
	}
}

func TestExtractPredictions(t *testing.T) {
	results := []*TraversalResult{
		{Prediction: "approved"},
		{Prediction: "denied"},
		nil, // Test nil handling
		{Prediction: "approved"},
	}

	predictions := ExtractPredictions(results)

	if len(predictions) != len(results) {
		t.Errorf("Expected %d predictions, got %d", len(results), len(predictions))
	}

	expected := []interface{}{"approved", "denied", nil, "approved"}
	for i, pred := range predictions {
		if pred != expected[i] {
			t.Errorf("Prediction %d: expected %v, got %v", i, expected[i], pred)
		}
	}
}

func TestAnalyzeConfidenceStats(t *testing.T) {
	results := []*TraversalResult{
		{Confidence: 0.9},
		{Confidence: 0.7},
		{Confidence: 0.3},
		{Confidence: 0.8},
	}

	stats := AnalyzeConfidenceStats(results)

	if stats.Mean != 0.675 {
		t.Errorf("Expected mean 0.675, got %f", stats.Mean)
	}

	if stats.Min != 0.3 {
		t.Errorf("Expected min 0.3, got %f", stats.Min)
	}

	if stats.Max != 0.9 {
		t.Errorf("Expected max 0.9, got %f", stats.Max)
	}

	if stats.LowCount != 1 {
		t.Errorf("Expected 1 low confidence, got %d", stats.LowCount)
	}

	if stats.HighCount != 2 {
		t.Errorf("Expected 2 high confidence, got %d", stats.HighCount)
	}
}

func TestErrorCases(t *testing.T) {
	t.Run("Nil tree", func(t *testing.T) {
		record := map[string]interface{}{"x": 1}
		_, err := TraverseTreeSimple(nil, record)
		if err == nil {
			t.Error("Expected error for nil tree")
		}
	})

	t.Run("Tree without root", func(t *testing.T) {
		tree, err := models.NewDecisionTree(models.CategoricalFeature, 5, 2)
		if err != nil {
			t.Fatalf("Failed to create tree: %v", err)
		}
		// tree.Root is nil

		record := map[string]interface{}{"x": 1}
		_, err = TraverseTreeSimple(tree, record)
		if err == nil {
			t.Error("Expected error for tree without root")
		}
	})

	t.Run("Nil record", func(t *testing.T) {
		tree, err := createSimpleTree()
		if err != nil {
			t.Fatalf("Failed to create tree: %v", err)
		}

		_, err = TraverseTreeSimple(tree, nil)
		if err == nil {
			t.Error("Expected error for nil record")
		}
	})

	t.Run("Nil batch records", func(t *testing.T) {
		tree, err := createSimpleTree()
		if err != nil {
			t.Fatalf("Failed to create tree: %v", err)
		}

		_, err = PredictBatch(tree, nil, DefaultTraversalOptions())
		if err == nil {
			t.Error("Expected error for nil records")
		}
	})
}

func TestFeatureValueValidation(t *testing.T) {
	tree, err := createSimpleTree()
	if err != nil {
		t.Fatalf("Failed to create tree: %v", err)
	}

	tests := []struct {
		name    string
		record  map[string]interface{}
		wantErr bool
	}{
		{
			name:    "Valid numeric and categorical",
			record:  map[string]interface{}{"age": 25.0, "sex": "male"},
			wantErr: false,
		},
		{
			name:    "Valid integer age",
			record:  map[string]interface{}{"age": 25, "sex": "male"},
			wantErr: false,
		},
		{
			name:    "Valid string numeric age",
			record:  map[string]interface{}{"age": "25.5", "sex": "male"},
			wantErr: false,
		},
		{
			name:    "Invalid string age",
			record:  map[string]interface{}{"age": "not_a_number", "sex": "male"},
			wantErr: true,
		},
		{
			name:    "Nil values allowed",
			record:  map[string]interface{}{"age": nil, "sex": "male"},
			wantErr: false,
		},
		{
			name:    "Missing features allowed",
			record:  map[string]interface{}{"age": 25.0},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := ValidateInputRecord(tt.record, tree)

			if tt.wantErr && err == nil {
				t.Error("Expected validation error but got none")
			}

			if !tt.wantErr && err != nil {
				t.Errorf("Unexpected validation error: %v", err)
			}
		})
	}
}

// Benchmark tests
func BenchmarkTraverseTreeSimple(b *testing.B) {
	tree, err := createSimpleTree()
	if err != nil {
		b.Fatalf("Failed to create tree: %v", err)
	}

	record := map[string]interface{}{"age": 25.0, "sex": "male"}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := TraverseTreeSimple(tree, record)
		if err != nil {
			b.Fatalf("TraverseTreeSimple failed: %v", err)
		}
	}
}

func BenchmarkTraverseRecursive(b *testing.B) {
	tree, err := createDeepTree(20)
	if err != nil {
		b.Fatalf("Failed to create tree: %v", err)
	}

	record := map[string]interface{}{"x": -1.0}
	options := TraversalOptions{
		Strategy: RecursiveTraversal,
		MaxDepth: 1000,
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := TraverseTree(tree, record, options)
		if err != nil {
			b.Fatalf("Recursive traversal failed: %v", err)
		}
	}
}

func BenchmarkTraverseIterative(b *testing.B) {
	tree, err := createDeepTree(20)
	if err != nil {
		b.Fatalf("Failed to create tree: %v", err)
	}

	record := map[string]interface{}{"x": -1.0}
	options := TraversalOptions{
		Strategy: IterativeTraversal,
		MaxDepth: 1000,
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := TraverseTree(tree, record, options)
		if err != nil {
			b.Fatalf("Iterative traversal failed: %v", err)
		}
	}
}

func BenchmarkPredictBatch(b *testing.B) {
	tree, err := createSimpleTree()
	if err != nil {
		b.Fatalf("Failed to create tree: %v", err)
	}

	records := make([]map[string]interface{}, 1000)
	for i := range records {
		records[i] = map[string]interface{}{"age": float64(i % 50), "sex": "male"}
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := PredictBatch(tree, records, DefaultTraversalOptions())
		if err != nil {
			b.Fatalf("PredictBatch failed: %v", err)
		}
	}
}

func BenchmarkAutoStrategy(b *testing.B) {
	// Clear cache before benchmark
	ClearDepthCache()

	tree, err := createDeepTree(RECURSIVE_THRESHOLD + 10)
	if err != nil {
		b.Fatalf("Failed to create deep tree: %v", err)
	}

	record := map[string]interface{}{"x": -1.0}
	options := TraversalOptions{
		Strategy: AutoStrategy,
		MaxDepth: 1000,
	}

	// Warm up the cache
	_, err = TraverseTree(tree, record, options)
	if err != nil {
		b.Fatalf("Warm up traversal failed: %v", err)
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := TraverseTree(tree, record, options)
		if err != nil {
			b.Fatalf("Auto strategy traversal failed: %v", err)
		}
	}
}

func BenchmarkDepthCaching(b *testing.B) {
	// Clear cache before benchmark
	ClearDepthCache()

	tree, err := createDeepTree(100)
	if err != nil {
		b.Fatalf("Failed to create deep tree: %v", err)
	}

	// Warm up the cache
	getTreeDepth(tree)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		getTreeDepth(tree)
	}
}

func BenchmarkDepthWithoutCaching(b *testing.B) {
	tree, err := createDeepTree(100)
	if err != nil {
		b.Fatalf("Failed to create deep tree: %v", err)
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		estimateTreeDepth(tree.Root)
	}
}
