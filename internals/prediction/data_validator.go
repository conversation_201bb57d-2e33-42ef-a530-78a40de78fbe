// Package prediction provides validation and inference functionality for decision tree models.
package prediction

import (
	"fmt"

	"github.com/berrijam/mulberri/internals/utils"
	"github.com/berrijam/mulberri/pkg/logger"
	"github.com/berrijam/mulberri/pkg/models"
)

// ValidationError represents errors that occur during prediction data validation
type ValidationError struct {
	Field    string // Field that caused the error
	Value    string // Value that caused the error
	Reason   string // Human-readable error description
	RowIndex int    // Row index where error occurred (-1 if not applicable)
}

func (e *ValidationError) Error() string {
	if e.RowIndex >= 0 {
		return fmt.Sprintf("validation failed for field '%s' at row %d with value '%s': %s",
			e.Field, e.RowIndex, e.Value, e.Reason)
	}
	return fmt.Sprintf("validation failed for field '%s' with value '%s': %s",
		e.Field, e.Value, e.Reason)
}

// ValidationResult contains the results of validation
type ValidationResult struct {
	ValidRecords   []utils.PredictionRecord // Records that passed validation
	InvalidRecords []InvalidRecord          // Records that failed validation
	Errors         []ValidationError        // All validation errors encountered
}

// InvalidRecord represents a record that failed validation
type InvalidRecord struct {
	Record utils.PredictionRecord // The original record
	Errors []ValidationError      // Validation errors for this record
}

// ValidatePredictionRecords validates prediction records against model constraints
//
// Parameters:
// - records: Prediction records to validate
// - model: Trained decision tree model containing feature constraints
//
// Returns:
// - ValidationResult: Contains valid records, invalid records, and errors
func ValidatePredictionRecords(records []utils.PredictionRecord, model *models.DecisionTree) (*ValidationResult, error) {
	if model == nil {
		return nil, &ValidationError{
			Field:  "model",
			Value:  "nil",
			Reason: "model cannot be nil",
		}
	}

	if len(model.Features) == 0 {
		return nil, &ValidationError{
			Field:  "model_features",
			Value:  "empty",
			Reason: "model must have features defined",
		}
	}

	if len(records) == 0 {
		return &ValidationResult{
			ValidRecords:   make([]utils.PredictionRecord, 0),
			InvalidRecords: make([]InvalidRecord, 0),
			Errors:         make([]ValidationError, 0),
		}, nil
	}

	result := &ValidationResult{
		ValidRecords:   make([]utils.PredictionRecord, 0, len(records)),
		InvalidRecords: make([]InvalidRecord, 0),
		Errors:         make([]ValidationError, 0),
	}

	for i, record := range records {
		recordErrors := validateSingleRecord(record, model, i)

		if len(recordErrors) == 0 {
			// Record is valid
			result.ValidRecords = append(result.ValidRecords, record)
		} else {
			// Record has validation errors
			result.InvalidRecords = append(result.InvalidRecords, InvalidRecord{
				Record: record,
				Errors: recordErrors,
			})
			result.Errors = append(result.Errors, recordErrors...)
		}
	}

	return result, nil
}

// validateSingleRecord validates a single prediction record
func validateSingleRecord(record utils.PredictionRecord, model *models.DecisionTree, rowIndex int) []ValidationError {
	var errors []ValidationError
	// Remove target column if present
	targetCol := model.TargetColumn
	if _, hasTarget := record.Features[targetCol]; hasTarget {
		delete(record.Features, targetCol)
		// Optionally, add a warning error:
		logger.Debug("removed target value from the record")
	}

	// Check for required features
	for featureName, feature := range model.Features {
		value, exists := record.Features[featureName]

		if !exists {
			errors = append(errors, ValidationError{
				Field:  featureName,
				Value:  "missing",
				Reason: "required feature not found in record",
				RowIndex: rowIndex,
			})
			continue
		}

		// Skip validation for nil values (missing data)
		if value == nil {
			continue
		}

		// Validate based on feature type
		switch feature.Type {
		case models.NumericFeature:
			if validationErr := validateNumericFeature(featureName, value, rowIndex); validationErr != nil {
				errors = append(errors, *validationErr)
			}
		case models.CategoricalFeature:
			if validationErr := validateCategoricalFeature(featureName, value, rowIndex); validationErr != nil {
				errors = append(errors, *validationErr)
			}

		}
	}

	return errors
}

// validateNumericFeature validates a numeric feature value
func validateNumericFeature(featureName string, value interface{}, rowIndex int) *ValidationError {
	// Check if value is numeric
	_, ok := value.(float64)
	if !ok {
		return &ValidationError{
			Field:  featureName,
			Value:  fmt.Sprintf("%v", value),
			Reason: "expected numeric value but got non-numeric type",
			RowIndex: rowIndex,
		}
	}

	return nil
}

// validateCategoricalFeature validates a categorical feature value
func validateCategoricalFeature(featureName string, value interface{}, rowIndex int) *ValidationError {
	// Check if value is string
	_, ok := value.(string)
	if !ok {
		return &ValidationError{
			Field:  featureName,
			Value:  fmt.Sprintf("%v", value),
			Reason: "expected string value but got non-string type",
			RowIndex: rowIndex,
		}
	}

	return nil
}

// ValidateAndFilterPredictionRecords validates records and returns only the valid ones
// This is a convenience function for when you only want valid records
func ValidateAndFilterPredictionRecords(records []utils.PredictionRecord, model *models.DecisionTree) ([]utils.PredictionRecord, error) {
	result, err := ValidatePredictionRecords(records, model)
	if err != nil {
		return nil, err
	}

	return result.ValidRecords, nil
}
